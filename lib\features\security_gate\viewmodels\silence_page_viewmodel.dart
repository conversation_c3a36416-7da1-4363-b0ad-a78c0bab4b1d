import 'dart:async';
import 'package:flutter/foundation.dart';

import '../models/gate_state.dart';
import '../models/gate_command.dart';
import '../models/silence_page_state.dart';
import '../services/gate_serial_service.dart';
import '../services/rfid_service.dart';
import '../services/book_api_service.dart';
import '../services/gate_auth_service.dart';
import '../services/enhanced_rfid_service.dart';
import '../models/book_info.dart';
import '../../auth/models/auth_result.dart';
import '../../auth/services/multi_auth_manager.dart';

/// 静默页面ViewModel
/// 负责管理页面状态、业务逻辑和数据流
class SilencePageViewModel extends ChangeNotifier {
  // 当前状态
  GateState _currentGateState = GateState.idle;
  SilencePageState _currentPageState = SilencePageState.welcome;
  UIContentData _contentData = const UIContentData();
  
  // 服务依赖
  late GateSerialService _serialService;
  late GateAuthService _gateAuthService;
  late RFIDService _rfidService;
  late BookApiService _bookApiService;
  
  // 事件订阅
  StreamSubscription? _commandSubscription;
  StreamSubscription? _authSubscription;
  StreamSubscription? _rfidBarcodeSubscription;
  StreamSubscription? _rfidCountSubscription;
  StreamSubscription? _serialErrorSubscription;
  StreamSubscription? _rfidErrorSubscription;
  
  // 状态超时控制
  Timer? _stateTimeoutTimer;
  Timer? _autoRecoverTimer;
  static const int _stateTimeoutSeconds = 30;
  
  // RFID扫描数据
  List<String> _scannedBarcodes = [];

  // 🔥 新增：书籍信息缓存
  Map<String, BookInfo> _scannedBooksInfo = {};

  // 当前认证用户信息
  String? _currentUserName;
  
  // Getters
  GateState get currentGateState => _currentGateState;
  SilencePageState get currentPageState => _currentPageState;
  UIContentData get contentData => _contentData;
  bool get showDefaultContent => _currentPageState.showDefaultContent;
  List<String> get scannedBarcodes => List.from(_scannedBarcodes);
  // 🔥 新增：书籍信息getter
  Map<String, BookInfo> get scannedBooksInfo => Map.unmodifiable(_scannedBooksInfo);
  
  /// 初始化ViewModel
  Future<void> initialize() async {
    try {
      debugPrint('开始初始化SilencePageViewModel...');
      
      // 1. 初始化服务
      await _initializeServices();
      
      // 2. 设置事件监听
      _setupEventListeners();
      
      // 3. 启动串口监听
      await _serialService.startListening();
      
      debugPrint('SilencePageViewModel初始化完成');
    } catch (e) {
      debugPrint('SilencePageViewModel初始化失败: $e');
      _updatePageState(SilencePageState.error, 
          UIContentData.error(message: '系统初始化失败: $e'));
      rethrow;
    }
  }
  
  /// 初始化服务
  Future<void> _initializeServices() async {
    // 初始化串口服务
    _serialService = GateSerialService.instance;
    await _serialService.initialize();

    // 初始化闸机认证服务
    _gateAuthService = GateAuthService.instance;
    await _gateAuthService.initialize();

    // 初始化RFID服务
    _rfidService = RFIDService.instance;
    await _rfidService.initialize();

    // 初始化书籍API服务
    _bookApiService = BookApiService.instance;
    _bookApiService.initialize();
  }
  
  /// 设置事件监听
  void _setupEventListeners() {
    // 监听串口命令
    _commandSubscription = _serialService.commandStream.listen(
      _handleSerialCommand,
      onError: (error) {
        debugPrint('串口命令流错误: $error');
        _handleError('串口通信错误: $error');
      },
    );
    
    // 监听串口错误
    _serialErrorSubscription = _serialService.errorStream.listen(
      (error) {
        debugPrint('串口错误: $error');
        _handleError(error);
      },
    );
    
    // 注意：认证结果监听将在启动认证时设置
    // 这里不需要预先设置监听
    
    // 监听RFID扫描结果
    _rfidBarcodeSubscription = _rfidService.barcodeStream.listen(
      _handleRFIDBarcode,
    );

    _rfidCountSubscription = _rfidService.countStream.listen(
      _handleRFIDCount,
    );

    // 🔥 新增：监听书籍扫描结果
    _rfidService.bookResultStream.listen(
      _handleBookScanResult,
      onError: (error) {
        debugPrint('书籍扫描结果流错误: $error');
      },
    );

    _rfidErrorSubscription = _rfidService.errorStream.listen(
      (error) {
        debugPrint('RFID错误: $error');
        _handleError('RFID扫描错误: $error');
      },
    );
  }
  
  /// 处理串口命令
  void _handleSerialCommand(GateCommand command) async {
    debugPrint('收到串口命令: ${command.type} (${command.displayName})');
    
    // 取消状态超时
    _cancelStateTimeout();
    _cancelAutoRecover();
    
    switch (command.type) {
      case GateCommand.enterStart:
        await _handleEnterStart();
        break;
      case GateCommand.enterEnd:
        await _handleEnterEnd();
        break;
      case GateCommand.exitStart:
        await _handleExitStart();
        break;
      case GateCommand.exitEnd:
        await _handleExitEnd();
        break;
      case GateCommand.positionReached:
        await _handlePositionReached();
        break;
      case GateCommand.tailgating:
        _handleTailgating();
        break;
      case GateCommand.doorBlocked:
        _handleDoorBlocked();
        break;
      default:
        debugPrint('未处理的串口命令: ${command.type}');
    }
  }
  
  /// 进馆开始处理
  Future<void> _handleEnterStart() async {
    debugPrint('处理进馆开始');

    _updateGateState(GateState.enterStarted);
    _updatePageState(SilencePageState.authenticating, UIContentData.authenticating());

    // 启动闸机认证服务（无UI模式）
    try {
      // 检查MultiAuthManager状态
      final multiAuthManager = MultiAuthManager.instance;
      debugPrint('MultiAuthManager当前状态: ${multiAuthManager.state}');

      // idle状态表示已初始化且准备就绪，可以直接启动认证服务
      if (multiAuthManager.state == MultiAuthState.error) {
        debugPrint('MultiAuthManager处于错误状态，无法启动认证');
        throw Exception('认证系统处于错误状态，请重启应用');
      }

      // 现在启动闸机认证服务
      // 注意：使用与MultiAuthManager初始化时相同的认证方式
      await _gateAuthService.startGateAuth(
        onResult: _handleGateAuthResult,
        onError: (error) {
          debugPrint('认证服务错误: $error');
          _handleError('认证系统错误: $error');
        },
        onStateChanged: (state) {
          debugPrint('认证状态变化: $state');
        },
        enabledMethods: [
          AuthMethod.face,                    // 人脸识别
          AuthMethod.readerCard,              // 读者证
          AuthMethod.wechatScanQRCode,        // 微信扫码（替代qrCode）
        ],
        timeoutSeconds: 30,
      );

      _updateGateState(GateState.enterScanning);
      _setStateTimeout();

      debugPrint('闸机认证服务启动成功');
    } catch (e) {
      debugPrint('启动认证系统失败: $e');
      _handleError('启动认证系统失败: $e');
    }
  }
  
  /// 处理闸机认证结果
  void _handleGateAuthResult(AuthResult result) {
    if (_currentGateState != GateState.enterScanning) {
      debugPrint('非认证状态收到认证结果，忽略');
      return;
    }

    debugPrint('收到闸机认证结果: ${result.status}, 用户: ${result.userName}');

    // 取消状态超时
    _cancelStateTimeout();

    if (result.status == AuthStatus.success) {
      // 存储当前用户信息
      _currentUserName = result.userName;

      // 认证成功
      _updateGateState(GateState.enterOpening);
      _updatePageState(SilencePageState.authSuccess,
          UIContentData.authSuccess(
            userName: result.userName ?? '未知用户',
            message: '${result.userName ?? '未知用户'}，同学 欢迎光临'
          ));

      // 发送开门命令
      _serialService.sendCommand('enter_open');

      // 设置自动恢复（认证服务将在进馆结束时停止）
      _setAutoRecover();

    } else {
      // 认证失败
      String errorMessage = result.errorMessage ?? '认证失败';

      // 根据失败类型显示不同消息
      if (result.status == AuthStatus.failureTimeout) {
        errorMessage = '认证超时，请重试';
      } else if (result.status == AuthStatus.failureNoMatch) {
        errorMessage = '未找到读者信息，请检查证件';
      } else if (result.status == AuthStatus.failureError) {
        errorMessage = '认证系统错误，请重试';
      }

      _updatePageState(SilencePageState.authFailed,
          UIContentData.authFailed(message: errorMessage));

      // 设置自动恢复，允许重新认证
      _setAutoRecover();
    }
  }
  
  /// 进馆结束处理
  Future<void> _handleEnterEnd() async {
    debugPrint('处理进馆结束');

    // 停止认证服务
    await _gateAuthService.stopGateAuth();

    _updateGateState(GateState.enterOver);

    // 短暂延迟后回到欢迎界面
    Timer(const Duration(seconds: 1), () {
      _resetToWelcome();
    });
  }
  
  /// 出馆开始处理
  Future<void> _handleExitStart() async {
    debugPrint('处理出馆开始');
    
    _updateGateState(GateState.exitStarted);
    _updatePageState(SilencePageState.rfidScanning, UIContentData.rfidScanning());
    
    // 启动RFID扫描
    try {
      _scannedBarcodes.clear();
      await _rfidService.startScanning();
      _updateGateState(GateState.exitScanning);
      _setStateTimeout();
    } catch (e) {
      debugPrint('启动RFID扫描失败: $e');
      _handleError('启动RFID扫描失败: $e');
    }
  }
  
  /// 处理RFID条码扫描
  void _handleRFIDBarcode(String barcode) {
    if (!_scannedBarcodes.contains(barcode)) {
      _scannedBarcodes.add(barcode);
      debugPrint('扫描到新条码: $barcode (总计: ${_scannedBarcodes.length})');
    }
  }
  
  /// 处理RFID扫描数量变化
  void _handleRFIDCount(int count) {
    if (_currentPageState == SilencePageState.rfidScanning) {
      _updatePageState(SilencePageState.rfidScanning,
          UIContentData.rfidScanning(scannedCount: count));
    }
  }

  /// 🔥 新增：处理书籍扫描结果
  void _handleBookScanResult(BookScanResult result) {
    // 根据状态处理不同的情况
    switch (result.status) {
      case BookScanStatus.processing:
        debugPrint('正在获取书籍信息: ${result.barcode}');
        break;
      case BookScanStatus.success:
        if (result.bookInfo != null) {
          _scannedBooksInfo[result.barcode] = result.bookInfo!;
          debugPrint('获取书籍信息: ${result.barcode} - ${result.bookInfo!.bookName} - ${result.bookInfo!.borrowStatusText}');
        }
        break;
      case BookScanStatus.failed:
        debugPrint('获取书籍信息失败: ${result.barcode} - ${result.error ?? "未知错误"}');
        break;
    }

    // 如果当前在扫描状态，更新UI显示
    if (_currentPageState == SilencePageState.rfidScanning) {
      _updatePageState(SilencePageState.rfidScanning,
          UIContentData.rfidScanning(
            scannedCount: _scannedBarcodes.length,
            booksInfo: _scannedBooksInfo.map((key, value) => MapEntry(key, value.toJson())),
          ));
    }

    // 通知监听者更新
    notifyListeners();
  }
  
  /// 到达指定位置处理
  Future<void> _handlePositionReached() async {
    if (_currentGateState == GateState.exitScanning) {
      debugPrint('用户到达指定位置，准备停止RFID扫描');
      
      _updateGateState(GateState.exitChecking);

      // 直接停止扫描并检查书籍，无延迟
      await _stopRFIDAndCheckBooks();
    }
  }
  
  /// 停止RFID扫描并检查书籍
  Future<void> _stopRFIDAndCheckBooks() async {
    try {
      // 停止RFID扫描
      final finalBarcodes = await _rfidService.stopScanning();
      _scannedBarcodes = finalBarcodes;
      
      debugPrint('RFID扫描结束，共扫描到${_scannedBarcodes.length}本书');
      
      if (_scannedBarcodes.isEmpty) {
        // 没有扫描到书籍，允许通过
        _updatePageState(SilencePageState.exitAllowed,
            UIContentData.authSuccess(
              userName: _currentUserName ?? '用户',
              message: '${_currentUserName ?? '用户'}，同学 欢迎再次光临'
            ));
        _serialService.sendCommand('exit_open');
        _setAutoRecover();
      } else {
        // 检查书籍状态
        await _checkBooksStatus();
      }
    } catch (e) {
      debugPrint('停止RFID扫描失败: $e');
      _handleError('书籍检查失败: $e');
    }
  }
  
  /// 检查书籍状态
  Future<void> _checkBooksStatus() async {
    try {
      final books = await _bookApiService.checkBooksStatus(_scannedBarcodes);
      final hasUnborrowedBooks = books.any((book) => !book.isBorrowed);
      
      if (hasUnborrowedBooks) {
        // 有未借书籍，禁止通过
        _updatePageState(SilencePageState.exitBlocked, 
            UIContentData.bookCheck(books: books, hasUnborrowedBooks: true));
      } else {
        // 所有书籍都已借阅，允许通过
        _updatePageState(SilencePageState.exitAllowed,
            UIContentData.authSuccess(
              userName: _currentUserName ?? '用户',
              message: '${_currentUserName ?? '用户'}，同学 欢迎再次光临'
            ));
        _serialService.sendCommand('exit_open');
      }
      
      _setAutoRecover();
    } catch (e) {
      debugPrint('检查书籍状态失败: $e');
      _handleError('书籍检查失败: $e');
    }
  }
  
  /// 出馆结束处理
  Future<void> _handleExitEnd() async {
    debugPrint('处理出馆结束');
    
    _updateGateState(GateState.exitOver);
    
    // 确保停止RFID扫描
    if (_rfidService.isScanning) {
      await _rfidService.stopScanning();
    }
    
    // 短暂延迟后回到欢迎界面
    Timer(const Duration(seconds: 1), () {
      _resetToWelcome();
    });
  }
  
  /// 处理尾随检测
  void _handleTailgating() {
    debugPrint('检测到尾随');
    // 可以显示警告信息，但不改变主要状态
  }
  
  /// 处理通道阻挡
  void _handleDoorBlocked() {
    debugPrint('检测到开门有人');
    // 可以显示警告信息，但不改变主要状态
  }
  
  /// 处理错误
  void _handleError(String errorMessage) {
    debugPrint('系统错误: $errorMessage');
    
    _updateGateState(GateState.error);
    _updatePageState(SilencePageState.error, 
        UIContentData.error(message: errorMessage));
    
    // 停止所有活动
    _stopAllActivities();
    
    // 5秒后自动恢复
    Timer(const Duration(seconds: 5), () {
      _resetToWelcome();
    });
  }
  
  /// 停止所有活动
  void _stopAllActivities() {
    // 停止认证服务
    _gateAuthService.stopGateAuth();

    // 停止RFID扫描
    if (_rfidService.isScanning) {
      _rfidService.stopScanning();
    }

    _cancelStateTimeout();
    _cancelAutoRecover();
  }
  
  /// 更新闸机状态
  void _updateGateState(GateState newState) {
    if (_currentGateState != newState) {
      _currentGateState = newState;
      debugPrint('闸机状态变更: $newState');
      notifyListeners();
    }
  }
  
  /// 更新页面状态
  void _updatePageState(SilencePageState newState, [UIContentData? data]) {
    _currentPageState = newState;
    if (data != null) {
      _contentData = data;
    }
    debugPrint('页面状态变更: $newState');
    notifyListeners();
  }
  
  /// 🔥 增强：重置到欢迎界面
  void _resetToWelcome() {
    _stopAllActivities();
    _scannedBarcodes.clear();
    // 🔥 清空书籍信息缓存
    _scannedBooksInfo.clear();
    _updateGateState(GateState.idle);
    _updatePageState(SilencePageState.welcome, const UIContentData());
  }
  
  /// 设置状态超时
  void _setStateTimeout() {
    _cancelStateTimeout();
    _stateTimeoutTimer = Timer(Duration(seconds: _stateTimeoutSeconds), () {
      debugPrint('状态超时，自动重置');
      _resetToWelcome();
    });
  }
  
  /// 取消状态超时
  void _cancelStateTimeout() {
    _stateTimeoutTimer?.cancel();
    _stateTimeoutTimer = null;
  }
  
  /// 设置自动恢复
  void _setAutoRecover() {
    if (_currentPageState.isTemporaryState) {
      _cancelAutoRecover();
      final seconds = _currentPageState.autoRecoverSeconds;
      if (seconds > 0) {
        _autoRecoverTimer = Timer(Duration(seconds: seconds), () {
          _resetToWelcome();
        });
      }
    }
  }
  
  /// 取消自动恢复
  void _cancelAutoRecover() {
    _autoRecoverTimer?.cancel();
    _autoRecoverTimer = null;
  }
  
  /// 手动重置系统
  void resetSystem() {
    debugPrint('手动重置系统');
    _resetToWelcome();
  }
  
  /// 模拟串口命令（用于调试）
  void simulateSerialCommand(String commandType) {
    final commandData = GateCommand.receiveCommandMap[commandType];
    if (commandData != null) {
      final command = GateCommand(type: commandType, data: commandData);
      _handleSerialCommand(command);
    } else {
      debugPrint('未知的命令类型: $commandType');
    }
  }
  
  /// 获取系统状态
  Map<String, dynamic> getSystemStatus() {
    return {
      'gate_state': _currentGateState.name,
      'page_state': _currentPageState.name,
      'scanned_count': _scannedBarcodes.length,
      'serial_status': _serialService.getStatus(),
      'rfid_status': _rfidService.getStatus(),
      'book_api_status': _bookApiService.getStatus(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
  
  @override
  void dispose() {
    debugPrint('释放SilencePageViewModel资源');
    
    _commandSubscription?.cancel();
    _authSubscription?.cancel();
    _rfidBarcodeSubscription?.cancel();
    _rfidCountSubscription?.cancel();
    _serialErrorSubscription?.cancel();
    _rfidErrorSubscription?.cancel();
    
    _cancelStateTimeout();
    _cancelAutoRecover();
    
    _stopAllActivities();
    
    super.dispose();
    
    debugPrint('SilencePageViewModel已释放');
  }
}
