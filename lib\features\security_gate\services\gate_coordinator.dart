import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';

import '../models/gate_state.dart';
import '../models/gate_command.dart';
import '../models/gate_event.dart';
import '../models/book_info.dart';
import 'gate_serial_service.dart';
import 'rfid_service.dart';
import 'enhanced_rfid_service.dart';
import '../../auth/services/multi_auth_manager.dart';
import '../../auth/models/auth_result.dart';

/// 闸机协调器 - 核心业务逻辑控制器
class GateCoordinator extends ChangeNotifier {
  static GateCoordinator? _instance;
  static GateCoordinator get instance => _instance ??= GateCoordinator._();
  GateCoordinator._();
  
  // 当前状态
  GateState _currentState = GateState.idle;
  GateState get currentState => _currentState;
  
  // 服务依赖
  late GateSerialService _serialService;
  late MultiAuthManager _authManager;
  
  // 事件订阅
  StreamSubscription? _commandSubscription;
  StreamSubscription? _authSubscription;
  StreamSubscription? _serialErrorSubscription;
  
  // 事件流
  final StreamController<GateEvent> _eventController = 
      StreamController<GateEvent>.broadcast();
  Stream<GateEvent> get eventStream => _eventController.stream;
  
  // RFID扫描相关
  List<String> _scannedBooks = [];
  // 🔥 新增：书籍信息缓存
  Map<String, BookInfo> _scannedBooksInfo = {};
  Timer? _rfidTimer;
  bool _isRfidScanning = false;
  
  // 状态超时控制
  Timer? _stateTimeoutTimer;
  static const int _stateTimeoutSeconds = 30; // 状态超时时间
  
  /// 初始化闸机协调器
  Future<void> initialize() async {
    try {
      debugPrint('开始初始化闸机协调器...');
      
      // 1. 初始化串口服务
      _serialService = GateSerialService.instance;
      await _serialService.initialize();
      
      // 2. 获取现有认证管理器实例
      _authManager = MultiAuthManager.instance;
      
      // 3. 监听串口命令
      _commandSubscription = _serialService.commandStream.listen(
        _handleSerialCommand,
        onError: (error) {
          debugPrint('串口命令流错误: $error');
          _handleError('串口通信错误: $error');
        },
      );
      
      // 4. 监听串口错误
      _serialErrorSubscription = _serialService.errorStream.listen(
        (error) {
          debugPrint('串口错误: $error');
          _handleError(error);
        },
      );
      
      // 5. 监听认证结果
      _authSubscription = _authManager.authResultStream.listen(
        _handleAuthResult,
        onError: (error) {
          debugPrint('认证结果流错误: $error');
          _handleError('认证系统错误: $error');
        },
      );
      
      // 6. 启动串口监听
      await _serialService.startListening();
      
      // 7. 设置初始状态
      _setState(GateState.idle);
      
      debugPrint('闸机协调器初始化完成');
      
      // 发送初始化完成事件
      _eventController.add(GateEvent(
        type: 'initialized',
        data: {'message': '安全闸机系统已就绪'},
      ));
      
    } catch (e) {
      final errorMsg = '闸机协调器初始化失败: $e';
      debugPrint(errorMsg);
      _handleError(errorMsg);
      rethrow;
    }
  }
  
  /// 处理串口命令
  void _handleSerialCommand(GateCommand command) {
    debugPrint('收到闸机命令: ${command.type} (${command.displayName})');
    
    // 取消状态超时
    _cancelStateTimeout();
    
    switch (command.type) {
      case GateCommand.enterStart:
        _handleEnterStart();
        break;
      case GateCommand.enterEnd:
        _handleEnterEnd();
        break;
      case GateCommand.exitStart:
        _handleExitStart();
        break;
      case GateCommand.exitEnd:
        _handleExitEnd();
        break;
      case GateCommand.positionReached:
        _handlePositionReached();
        break;
      case GateCommand.tailgating:
        _handleTailgating();
        break;
      case GateCommand.doorBlocked:
        _handleDoorBlocked();
        break;
      default:
        debugPrint('未处理的闸机命令: ${command.type}');
    }
  }
  
  /// 进馆开始 - 启动现有认证系统
  void _handleEnterStart() {
    if (_currentState != GateState.idle) {
      debugPrint('警告：非空闲状态收到进馆开始命令，当前状态: $_currentState');
      return;
    }
    
    _setState(GateState.enterStarted);
    
    // 发送进馆开始事件
    _eventController.add(GateEvent.createEnterStart());
    
    // 启动认证系统
    _startAuthentication();
    
    // 设置状态超时
    _setStateTimeout();
  }
  
  /// 启动认证（复用现有系统）
  void _startAuthentication() {
    try {
      debugPrint('启动现有认证系统...');
      
      // 设置认证状态
      _setState(GateState.enterScanning);
      
      // 直接使用现有的MultiAuthManager
      _authManager.startListening();
      
      debugPrint('认证系统已启动');
    } catch (e) {
      debugPrint('启动认证系统失败: $e');
      _handleError('启动认证系统失败: $e');
      _setState(GateState.error);
    }
  }
  
  /// 处理认证结果（来自现有认证系统）
  void _handleAuthResult(AuthResult result) {
    if (_currentState != GateState.enterScanning) {
      debugPrint('非认证状态收到认证结果，忽略');
      return;
    }
    
    debugPrint('收到认证结果: ${result.status}, 用户: ${result.userName}');
    
    if (result.status == AuthStatus.success) {
      debugPrint('认证成功: ${result.userName}');
      
      // 发送开门命令到闸机
      _serialService.sendCommand('enter_open');
      _setState(GateState.enterOpening);
      
      // 通知UI显示成功信息
      _eventController.add(GateEvent.createAuthSuccess(
        userName: result.userName ?? '未知用户',
        userId: result.userId,
      ));
      
      // 停止认证监听
      _authManager.stopListening();
      
    } else {
      debugPrint('认证失败: ${result.errorMessage}');
      
      // 发送失败信号到闸机
      _serialService.sendCommand('failure_signal');
      
      // 通知UI显示失败信息
      _eventController.add(GateEvent.createAuthFailed(
        message: result.errorMessage ?? '认证失败，请重试',
      ));
      
      // 重置状态，允许重新认证
      _setState(GateState.idle);
      
      // 停止认证监听
      _authManager.stopListening();
    }
  }
  
  /// 出馆开始 - 启动RFID扫描
  void _handleExitStart() {
    if (_currentState != GateState.idle) {
      debugPrint('警告：非空闲状态收到出馆开始命令，当前状态: $_currentState');
      return;
    }
    
    _setState(GateState.exitStarted);
    
    // 清空之前的扫描结果
    _scannedBooks.clear();
    
    // 通知UI显示扫描界面
    _eventController.add(GateEvent.createExitStart());
    
    // 启动RFID扫描
    _startRFIDScanning();
    
    // 设置状态超时
    _setStateTimeout();
  }
  
  /// 🔥 增强：启动RFID扫描（集成书籍信息获取）
  void _startRFIDScanning() {
    if (_isRfidScanning) {
      debugPrint('增强RFID扫描已在进行中');
      return;
    }

    debugPrint('启动增强RFID扫描');
    _setState(GateState.exitScanning);
    _isRfidScanning = true;

    // 🔥 使用增强的RFID服务
    RFIDService.instance.startScanning().then((_) {
      // 监听条码流
      RFIDService.instance.barcodeStream.listen((barcode) {
        _onBookScanned(barcode);
      });

      // 🔥 监听书籍扫描结果流
      RFIDService.instance.bookResultStream.listen((result) {
        _onBookScanResult(result);
      });

      debugPrint('增强RFID扫描监听器已设置');
    }).catchError((e) {
      debugPrint('启动增强RFID扫描失败: $e');
      // 回退到模拟扫描
      _simulateRFIDScanning();
    });
  }
  
  /// 模拟RFID扫描（实际项目中替换为真实插件）
  void _simulateRFIDScanning() {
    _rfidTimer = Timer.periodic(Duration(milliseconds: 800), (timer) {
      if (!_isRfidScanning) {
        timer.cancel();
        return;
      }
      
      // 模拟随机扫描到书籍
      if (Random().nextBool()) {
        final barcode = 'BOOK${Random().nextInt(1000).toString().padLeft(3, '0')}';
        _onBookScanned(barcode);
      }
    });
  }
  
  /// 处理扫描到的书籍
  void _onBookScanned(String barcode) {
    if (!_isRfidScanning) return;
    
    // 避免重复扫描
    if (_scannedBooks.contains(barcode)) return;
    
    _scannedBooks.add(barcode);
    debugPrint('扫描到书籍: $barcode (总计: ${_scannedBooks.length})');
    
    // 发送书籍扫描事件
    _eventController.add(GateEvent.createBookScanned(
      barcode: barcode,
      totalCount: _scannedBooks.length,
    ));
  }

  /// 🔥 新增：处理书籍扫描结果（包含书籍信息）
  void _onBookScanResult(BookScanResult result) {
    if (!_isRfidScanning) return;

    // 根据状态处理不同的情况
    switch (result.status) {
      case BookScanStatus.processing:
        debugPrint('正在获取书籍信息: ${result.barcode}');
        break;
      case BookScanStatus.success:
        if (result.bookInfo != null) {
          _scannedBooksInfo[result.barcode] = result.bookInfo!;
          debugPrint('获取书籍信息: ${result.barcode} - ${result.bookInfo!.bookName} - ${result.bookInfo!.borrowStatusText}');
        }
        break;
      case BookScanStatus.failed:
        debugPrint('获取书籍信息失败: ${result.barcode} - ${result.error ?? "未知错误"}');
        break;
    }

    // 发送增强的书籍扫描事件
    _eventController.add(GateEvent.createEnhancedBookScanned(
      barcode: result.barcode,
      bookInfo: result.bookInfo,
      totalCount: _scannedBooks.length,
      status: result.status.toString(),
    ));
  }

  /// 到达指定位置 - 停止扫描，检查书籍
  void _handlePositionReached() {
    if (_currentState == GateState.exitStarted || _currentState == GateState.exitScanning) {
      debugPrint('用户到达指定位置，准备停止RFID扫描');
      _setState(GateState.exitChecking);
      
      // 延迟1秒后停止扫描，确保最后的书籍被扫描到
      Timer(Duration(seconds: 1), () {
        _stopRFIDScanning();
        _checkBooksAndDecide();
      });
    } else {
      debugPrint('非出馆状态收到位置到达命令，当前状态: $_currentState');
    }
  }
  
  /// 🔥 增强：停止RFID扫描
  void _stopRFIDScanning() {
    if (!_isRfidScanning) return;

    _isRfidScanning = false;
    _rfidTimer?.cancel();
    _rfidTimer = null;

    debugPrint('停止增强RFID扫描，共扫描到${_scannedBooks.length}本书');

    // 🔥 停止增强RFID服务
    RFIDService.instance.stopScanning().then((result) {
      debugPrint('增强RFID扫描已停止，最终结果: ${result.length}个条码');
    }).catchError((e) {
      debugPrint('停止增强RFID扫描失败: $e');
    });
  }
  
  /// 🔥 增强：检查书籍状态并决定是否开门（使用已缓存的书籍信息）
  Future<void> _checkBooksAndDecide() async {
    debugPrint('开始检查书籍状态，共${_scannedBooks.length}本书');

    if (_scannedBooks.isEmpty) {
      debugPrint('未扫描到书籍，允许通过');
      _allowExit('未检测到书籍，请通过');
      return;
    }

    try {
      // 🔥 优先使用已缓存的书籍信息
      List<BookInfo> books = [];
      List<String> missingBarcodes = [];

      for (String barcode in _scannedBooks) {
        if (_scannedBooksInfo.containsKey(barcode)) {
          books.add(_scannedBooksInfo[barcode]!);
        } else {
          missingBarcodes.add(barcode);
        }
      }

      debugPrint('缓存命中: ${books.length}本，需要补充获取: ${missingBarcodes.length}本');

      // 🔥 补充获取缺失的书籍信息
      if (missingBarcodes.isNotEmpty) {
        final additionalBooks = await _checkBooksStatus(missingBarcodes);
        books.addAll(additionalBooks);

        // 更新缓存
        for (BookInfo book in additionalBooks) {
          _scannedBooksInfo[book.barcode] = book;
        }
      }

      final result = BookCheckResult(books: books);

      // 通知UI显示书籍列表
      _eventController.add(GateEvent.createShowBooks(
        books: books.map((book) => book.toJson()).toList(),
      ));

      // 判断是否允许通过
      if (result.allowPass) {
        _allowExit(result.summary, books);
      } else {
        _blockExit(result.summary, result.books, result.unborrowedBooks);
      }

    } catch (e) {
      debugPrint('检查书籍状态失败: $e');
      // 出错时默认允许通过，避免阻挡用户
      _allowExit('书籍检查服务异常，默认允许通过');
    }
  }
  
  /// 模拟书籍状态检查（实际项目中替换为真实API调用）
  Future<List<BookInfo>> _checkBooksStatus(List<String> barcodes) async {
    debugPrint('调用书籍检查API，检查${barcodes.length}本书');
    
    // TODO: 调用实际的API接口
    // final response = await http.post(
    //   Uri.parse('http://api.library.com/books/check'),
    //   body: jsonEncode({'barcodes': barcodes}),
    //   headers: {'Content-Type': 'application/json'},
    // );
    // final data = jsonDecode(response.body);
    // return data.map((item) => BookInfo.fromJson(item)).toList();
    
    // 模拟API响应延迟
    await Future.delayed(Duration(seconds: 1));
    
    // 模拟返回书籍信息
    return barcodes.map((barcode) {
      final random = Random();
      return BookInfo(
        barcode: barcode,
        bookName: '图书名称_${barcode.substring(4)}',
        author: '作者${random.nextInt(100)}',
        isbn: '978-${random.nextInt(1000000000)}',
        isBorrowed: random.nextBool(), // 随机模拟借阅状态
        borrowDate: random.nextBool() ? DateTime.now().subtract(Duration(days: random.nextInt(30))) : null,
        borrowerName: random.nextBool() ? '借阅者${random.nextInt(100)}' : null,
      );
    }).toList();
  }
  
  /// 允许出馆
  void _allowExit(String message, [List<BookInfo>? books]) {
    debugPrint('允许出馆: $message');
    
    // 发送开门命令到闸机
    _serialService.sendCommand('exit_open');
    
    // 发送允许出馆事件
    _eventController.add(GateEvent.createExitAllowed(
      message: message,
      books: books?.map((book) => book.toJson()).toList(),
    ));
  }
  
  /// 阻止出馆
  void _blockExit(String message, List<BookInfo> books, List<BookInfo> unborrowedBooks) {
    debugPrint('阻止出馆: $message');
    
    // 发送阻止出馆事件
    _eventController.add(GateEvent.createExitBlocked(
      message: message,
      books: books.map((book) => book.toJson()).toList(),
      unborrowedBooks: unborrowedBooks.map((book) => book.toJson()).toList(),
    ));
  }
  
  /// 进馆结束
  void _handleEnterEnd() {
    debugPrint('进馆流程结束');
    _setState(GateState.idle);
    
    _eventController.add(GateEvent(
      type: GateEvent.enterEnd,
      data: {'message': '进馆完成'},
    ));
    
    _cancelStateTimeout();
  }
  
  /// 🔥 增强：出馆结束
  void _handleExitEnd() {
    debugPrint('出馆流程结束');

    // 确保停止RFID扫描
    _stopRFIDScanning();

    // 🔥 清空书籍信息缓存
    _scannedBooksInfo.clear();

    _setState(GateState.idle);

    _eventController.add(GateEvent(
      type: GateEvent.exitEnd,
      data: {'message': '出馆完成'},
    ));

    _cancelStateTimeout();
  }
  
  /// 处理尾随检测
  void _handleTailgating() {
    debugPrint('检测到尾随');
    _eventController.add(GateEvent(
      type: GateEvent.tailgating,
      data: {'message': '检测到尾随，请单人通过'},
    ));
  }
  
  /// 处理通道阻挡
  void _handleDoorBlocked() {
    debugPrint('检测到开门有人');
    _eventController.add(GateEvent(
      type: GateEvent.doorBlocked,
      data: {'message': '通道被阻挡，请清理通道'},
    ));
  }
  
  /// 处理错误
  void _handleError(String errorMessage) {
    debugPrint('闸机系统错误: $errorMessage');
    
    _setState(GateState.error);
    
    _eventController.add(GateEvent(
      type: GateEvent.error,
      data: {'message': errorMessage},
    ));
    
    // 错误状态下停止所有活动
    _stopRFIDScanning();
    _authManager.stopListening();
    _cancelStateTimeout();
    
    // 5秒后自动恢复到空闲状态
    Timer(Duration(seconds: 5), () {
      if (_currentState == GateState.error) {
        _setState(GateState.idle);
      }
    });
  }
  
  /// 设置状态超时
  void _setStateTimeout() {
    _cancelStateTimeout();
    _stateTimeoutTimer = Timer(Duration(seconds: _stateTimeoutSeconds), () {
      debugPrint('状态超时，自动重置到空闲状态');
      _handleTimeout();
    });
  }
  
  /// 取消状态超时
  void _cancelStateTimeout() {
    _stateTimeoutTimer?.cancel();
    _stateTimeoutTimer = null;
  }
  
  /// 处理状态超时
  void _handleTimeout() {
    debugPrint('状态超时，当前状态: $_currentState');
    
    // 停止所有活动
    _stopRFIDScanning();
    _authManager.stopListening();
    
    // 重置到空闲状态
    _setState(GateState.idle);
    
    // 发送超时事件
    _eventController.add(GateEvent(
      type: 'timeout',
      data: {'message': '操作超时，系统已重置'},
    ));
  }
  
  /// 设置状态
  void _setState(GateState newState) {
    if (_currentState != newState) {
      final oldState = _currentState;
      _currentState = newState;
      
      debugPrint('闸机状态变更: $oldState -> $newState');
      
      // 发送状态变更事件
      _eventController.add(GateEvent(
        type: GateEvent.stateChanged,
        data: {
          'old_state': oldState.name,
          'new_state': newState.name,
          'message': newState.displayName,
        },
      ));
      
      notifyListeners();
    }
  }
  
  /// 获取系统状态信息
  Map<String, dynamic> getSystemStatus() {
    return {
      'current_state': _currentState.name,
      'state_display_name': _currentState.displayName,
      'is_rfid_scanning': _isRfidScanning,
      'scanned_books_count': _scannedBooks.length,
      'serial_status': _serialService.getStatus(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
  
  /// 手动重置系统
  void resetSystem() {
    debugPrint('手动重置闸机系统');

    _stopRFIDScanning();
    _authManager.stopListening();
    _cancelStateTimeout();
    _scannedBooks.clear();

    _setState(GateState.idle);

    _eventController.add(GateEvent(
      type: 'reset',
      data: {'message': '系统已手动重置'},
    ));
  }

  /// 模拟串口命令（用于调试）
  void simulateSerialCommand(String commandType) {
    final commandData = GateCommand.receiveCommandMap[commandType];
    if (commandData != null) {
      final command = GateCommand(type: commandType, data: commandData);
      _handleSerialCommand(command);
    } else {
      debugPrint('未知的命令类型: $commandType');
    }
  }
  
  /// 释放资源
  @override
  void dispose() {
    debugPrint('释放闸机协调器资源');
    
    _commandSubscription?.cancel();
    _authSubscription?.cancel();
    _serialErrorSubscription?.cancel();
    
    _stopRFIDScanning();
    _cancelStateTimeout();
    
    _serialService.dispose();
    _eventController.close();
    
    super.dispose();
    
    debugPrint('闸机协调器已释放');
  }
}
