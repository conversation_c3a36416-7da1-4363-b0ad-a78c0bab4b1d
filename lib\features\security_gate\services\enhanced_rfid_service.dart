import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:hardware/hardware.dart';
import 'package:seasetting/seasetting.dart';

import '../../../core/services/hardware_service.dart';
import '../../../features/auth/services/reader_service.dart';
import '../models/book_info.dart';
import '../models/book_scan_result.dart';
import 'book_info_api_service.dart';
import 'sip2_book_service.dart';

/// 增强的RFID扫描服务
/// 借鉴 sea_mini_smart_library_client 的硬件管理技术
/// 集成书籍信息获取功能
class EnhancedRFIDService {
  static EnhancedRFIDService? _instance;
  static EnhancedRFIDService get instance => _instance ??= EnhancedRFIDService._();
  EnhancedRFIDService._();
  
  // 🔥 借鉴：连接缓存机制
  static final Map<String, dynamic> _connectionCache = {};
  static final Map<String, DateTime> _connectionLastUsed = {};
  static final Map<String, bool> _connectionHealth = {};
  
  // 🔥 借鉴：硬件服务
  final HardwareService _hardwareService = HardwareService();
  
  // 书籍信息API服务
  final BookInfoApiService _bookInfoService = BookInfoApiService.instance;
  final Sip2BookService _sip2BookService = Sip2BookService.instance;
  
  // 阅读器配置
  List<HWReaderSettingData> _readerConfigs = [];
  
  // 扫描状态
  bool _isScanning = false;
  bool _isInitialized = false;
  String? _errorMessage;
  
  // 扫描结果
  final List<String> _scannedBarcodes = [];
  final Map<String, BookInfo> _bookInfoCache = {};
  final Set<String> _processedBarcodes = {};
  
  // 事件流
  final StreamController<String> _barcodeController = 
      StreamController<String>.broadcast();
  Stream<String> get barcodeStream => _barcodeController.stream;
  
  final StreamController<BookScanResult> _bookResultController = 
      StreamController<BookScanResult>.broadcast();
  Stream<BookScanResult> get bookResultStream => _bookResultController.stream;
  
  final StreamController<int> _countController = 
      StreamController<int>.broadcast();
  Stream<int> get countStream => _countController.stream;
  
  final StreamController<String> _errorController = 
      StreamController<String>.broadcast();
  Stream<String> get errorStream => _errorController.stream;
  
  // 模拟扫描定时器（实际项目中删除）
  Timer? _mockScanTimer;
  
  // Getters
  bool get isScanning => _isScanning;
  bool get isInitialized => _isInitialized;
  String? get errorMessage => _errorMessage;
  int get scannedCount => _scannedBarcodes.length;
  List<String> get scannedBarcodes => List.unmodifiable(_scannedBarcodes);
  
  /// 🔥 借鉴：从SettingProvider加载配置
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('增强RFID服务已经初始化');
      return;
    }
    
    try {
      debugPrint('开始初始化增强RFID服务...');
      
      // 🔥 借鉴：获取阅读器配置
      _readerConfigs = Get.context?.read<SettingProvider>()
          .readerConfigData?.bookReaders ?? [];
      
      if (_readerConfigs.isEmpty) {
        debugPrint('警告: 未找到书籍阅读器配置，使用默认配置');
        _readerConfigs = _generateDefaultReaderConfig();
      }
      
      // 🔥 借鉴：初始化硬件服务
      await _hardwareService.initializeAllHardware(_readerConfigs);
      
      // 初始化书籍API服务
      _bookInfoService.initialize();

      // 🔥 新增：初始化SIP2图书信息服务
      _sip2BookService.initialize();
      
      _isInitialized = true;
      _clearError();
      debugPrint('增强RFID服务初始化完成，配置了${_readerConfigs.length}个阅读器');
    } catch (e) {
      final errorMsg = '增强RFID服务初始化失败: $e';
      debugPrint(errorMsg);
      _setError(errorMsg);
      rethrow;
    }
  }
  
  /// 🔥 借鉴：生成默认阅读器配置
  List<HWReaderSettingData> _generateDefaultReaderConfig() {
    // 这里可以根据实际需要配置默认的阅读器
    // 暂时返回空列表，使用模拟扫描
    return [];
  }
  
  /// 开始增强扫描
  Future<void> startEnhancedScanning() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    if (_isScanning) {
      debugPrint('增强RFID扫描已在进行中');
      return;
    }
    
    try {
      debugPrint('开始增强RFID扫描');
      
      // 清空之前的扫描结果
      _scannedBarcodes.clear();
      _bookInfoCache.clear();
      _processedBarcodes.clear();
      
      if (_readerConfigs.isNotEmpty) {
        // 🔥 借鉴：配置阅读器
        await ReaderManager.instance.changeReaders(
          jsonEncode(_readerConfigs.map((e) => e.toJson()).toList())
        );
        
        // 🔥 借鉴：智能连接（网口优先，串口备用）
        await _smartConnect();

        // 启动扫描
        ReaderManager.instance.startInventory();
        
        // 🔥 借鉴：监听标签数据
        _startTagDataListening();
      } else {
        // 使用模拟扫描进行测试
        _startMockScanning();
      }
      
      _isScanning = true;
      _clearError();
      debugPrint('增强RFID扫描已启动');
    } catch (e) {
      final errorMsg = '启动增强RFID扫描失败: $e';
      debugPrint(errorMsg);
      _setError(errorMsg);
      rethrow;
    }
  }
  
  /// 🔥 借鉴：智能连接逻辑
  Future<void> _smartConnect() async {
    // 检查是否有网口配置
    final networkConfig = _findNetworkConfig();
    
    if (networkConfig != null) {
      debugPrint('使用网口连接: ${networkConfig['ipAddress']}:${networkConfig['port']}');
      await _connectWithNetwork(networkConfig);
    } else {
      debugPrint('使用串口连接');
      await _connectWithSerial();
    }
  }
  
  /// 🔥 借鉴：网口配置检测（支持LSGate和UHF阅读器）
  Map<String, String>? _findNetworkConfig() {
    for (HWReaderSettingData config in _readerConfigs) {
      String? ipAddress;
      String? netPort;
      String readerTypeName = '';
      
      // 检查UHF阅读器（荣瑞2881等）
      if (config.info is HWUHFInfoData) {
        HWUHFInfoData info = config.info as HWUHFInfoData;
        ipAddress = info.valueForKey('ipAddress');
        netPort = info.valueForKey('netPort');
        readerTypeName = 'UHF阅读器';
      }
      // 🔥 新增：检查LSGate图书馆安全门RFID阅读器
      else if (config.info is HWLSGateInfoData) {
        HWLSGateInfoData info = config.info as HWLSGateInfoData;
        ipAddress = info.valueForKey('ipAddress');
        netPort = info.valueForKey('netPort');
        readerTypeName = 'LSGate图书馆安全门RFID阅读器';
      }
      
      if (ipAddress?.isNotEmpty == true && netPort?.isNotEmpty == true) {
        debugPrint('找到网口配置: $readerTypeName - $ipAddress:$netPort');
        return {
          'ipAddress': ipAddress!,
          'port': netPort!,
          'readerId': config.id.toString(),
          'readerType': readerTypeName,
        };
      }
    }
    return null;
  }
  
  /// 🔥 借鉴：网口连接（带缓存）
  Future<void> _connectWithNetwork(Map<String, String> config) async {
    final cacheKey = '${config['ipAddress']}:${config['port']}';
    
    // 检查缓存
    if (_connectionCache.containsKey(cacheKey) && 
        _isConnectionHealthy(cacheKey)) {
      debugPrint('复用网口连接缓存: $cacheKey');
      return;
    }
    
    // 创建新连接
    try {
      ReaderManager.instance.open();
      ReaderManager.instance.untilDeteted();
      
      // 更新缓存
      _connectionCache[cacheKey] = true;
      _connectionLastUsed[cacheKey] = DateTime.now();
      _connectionHealth[cacheKey] = true;
      
      debugPrint('网口连接成功: $cacheKey');
    } catch (e) {
      _connectionHealth[cacheKey] = false;
      throw Exception('网口连接失败: $e');
    }
  }
  
  /// 🔥 借鉴：串口连接（带缓存）
  Future<void> _connectWithSerial() async {
    try {
      await ReaderManager.instance.open();
      await ReaderManager.instance.untilDeteted();
      debugPrint('串口连接成功');
    } catch (e) {
      throw Exception('串口连接失败: $e');
    }
  }
  
  /// 🔥 借鉴：标签数据监听
  void _startTagDataListening() {
    final tagProvider = Get.context?.read<HWTagProvider>();
    if (tagProvider != null) {
      tagProvider.addListener(_onTagDataChanged);
      debugPrint('已添加标签数据监听器');
    } else {
      debugPrint('警告: 无法获取HWTagProvider');
    }
  }
  
  /// 🔥 借鉴：标签数据处理
  void _onTagDataChanged() {
    final tagProvider = Get.context?.read<HWTagProvider>();
    if (tagProvider != null && tagProvider.tagList.isNotEmpty && tagProvider.type == HWTagType.addedItem) {
      debugPrint('检测到标签数据: ${tagProvider.tagList.length}条');
      
      for (HWTagData tag in tagProvider.tagList) {
        // 🔥 借鉴：条码提取逻辑
        List<String> barcodes = _extractBarcodes(tag);
        for (String barcode in barcodes) {
          if (barcode.isNotEmpty && !_processedBarcodes.contains(barcode)) {
            _processedBarcodes.add(barcode);
            _onBarcodeScanned(barcode);
          }
        }
      }
    }
  }
  
  /// 🔥 借鉴：多字段条码提取（增强解码器支持）
  List<String> _extractBarcodes(HWTagData tag) {
    List<String> barcodes = [];

    // 1. 优先从barCode字段提取（已解码的条码）
    if (tag.barCode?.isNotEmpty ?? false) {
      barcodes.add(tag.barCode!);
      debugPrint('从barCode字段提取条码: ${tag.barCode}');
    }

    // 2. 从oidList提取（解码后的数据）
    if (tag.oidList?.isNotEmpty ?? false) {
      for (var oid in tag.oidList!) {
        if (oid.oid == 1 && oid.data.isNotEmpty) {
          barcodes.add(oid.data);
          debugPrint('从oidList提取条码: ${oid.data}');
        }
      }
    }

    // 3. 从info字段提取（解码后的条码）
    if (tag.info?.containsKey('barCode') ?? false) {
      String? infoBarcode = tag.info!['barCode'];
      if (infoBarcode?.isNotEmpty ?? false) {
        barcodes.add(infoBarcode!);
        debugPrint('从info字段提取条码: $infoBarcode');
      }
    }

    // 4. 🔥 新增：尝试使用解码器解码UID
    if (barcodes.isEmpty && tag.uid?.isNotEmpty == true) {
      String? decodedBarcode = _decodeUidToBarcode(tag);
      if (decodedBarcode?.isNotEmpty == true) {
        barcodes.add(decodedBarcode!);
        debugPrint('通过解码器解码UID得到条码: $decodedBarcode');
      } else {
        // 如果解码失败，记录原始UID但不作为有效条码
        debugPrint('解码器解码失败，原始UID: ${tag.uid}');
        // 暂时不添加原始UID，避免无效的API调用
        // barcodes.add(tag.uid!);
      }
    }

    return barcodes.where((barcode) => barcode.isNotEmpty).toList();
  }

  /// 🔥 新增：使用解码器将UID解码为图书条码
  String? _decodeUidToBarcode(HWTagData tag) {
    try {
      // 获取当前阅读器配置
      if (_readerConfigs.isEmpty) {
        debugPrint('没有阅读器配置，无法解码');
        return null;
      }

      // 获取第一个阅读器配置（通常只有一个）
      final readerConfig = _readerConfigs.first;

      // 获取解码器类型
      final decoderType = readerConfig.info?.valueForKey('decoderType');
      if (decoderType == null) {
        debugPrint('阅读器未配置解码器类型');
        return null;
      }

      debugPrint('使用解码器: $decoderType 解码UID: ${tag.uid}');

      // 🔥 借鉴：使用ReaderService获取解码器
      final readerService = ReaderService.instance;
      final coder = readerService.getCoder(tag.tagFrequency, readerConfig);

      if (coder == null) {
        debugPrint('无法获取解码器: $decoderType');
        return null;
      }

      // 🔥 借鉴：执行解码 - 使用正确的方法名
      String? decodeResult;
      try {
        // 尝试使用不同的解码方法
        if (coder.runtimeType.toString().contains('UHF')) {
          // 超高频解码器可能有特殊的解码方法
          decodeResult = _tryUHFDecode(coder, tag, readerConfig);
        } else {
          // 通用解码器处理
          decodeResult = _tryGeneralDecode(coder, tag, readerConfig);
        }
      } catch (e) {
        debugPrint('解码器调用失败: $e');
        return null;
      }

      if (decodeResult?.isNotEmpty == true) {
        debugPrint('解码成功: ${tag.uid} -> $decodeResult');
        return decodeResult;
      } else {
        debugPrint('解码失败，返回空结果');
        return null;
      }

    } catch (e) {
      debugPrint('解码过程出错: $e');
      return null;
    }
  }

  /// 尝试超高频解码
  String? _tryUHFDecode(dynamic coder, HWTagData tag, HWReaderSettingData config) {
    try {
      // 对于超高频标签，通常需要从UID中提取条码
      // 根据配置的解码规则进行处理
      final coderConfig = config.coderConfig;
      if (coderConfig != null) {
        String uid = tag.uid ?? '';

        // 应用前缀和后缀规则
        String result = uid;
        final prefixStr = coderConfig.prefixStr;
        if (prefixStr != null && prefixStr.isNotEmpty) {
          result = prefixStr + result;
        }
        final suffixStr = coderConfig.suffixStr;
        if (suffixStr != null && suffixStr.isNotEmpty) {
          result = result + suffixStr;
        }

        // 应用替换规则
        final replaceRuleStr = coderConfig.replaceRuleStr;
        if (replaceRuleStr != null && replaceRuleStr.isNotEmpty) {
          final rules = replaceRuleStr.split('|');
          if (rules.length >= 2) {
            result = result.replaceAll(rules[0], rules[1]);
          }
        }

        return result.isNotEmpty ? result : null;
      }

      return tag.uid;
    } catch (e) {
      debugPrint('超高频解码失败: $e');
      return null;
    }
  }

  /// 尝试通用解码
  String? _tryGeneralDecode(dynamic coder, HWTagData tag, HWReaderSettingData config) {
    try {
      // 对于通用解码器，直接返回UID或应用基本规则
      final coderConfig = config.coderConfig;
      if (coderConfig != null) {
        String uid = tag.uid ?? '';

        // 应用书籍代码规则
        final bookCode = coderConfig.bookCode;
        if (bookCode != null && bookCode.isNotEmpty) {
          // 如果UID以书籍代码开头，则提取后面的部分
          if (uid.startsWith(bookCode)) {
            uid = uid.substring(bookCode.length);
          }
        }

        return uid.isNotEmpty ? uid : null;
      }

      return tag.uid;
    } catch (e) {
      debugPrint('通用解码失败: $e');
      return null;
    }
  }
  
  /// 处理扫描到的条码
  void _onBarcodeScanned(String barcode) {
    if (!_isScanning) return;
    
    // 避免重复扫描
    if (_scannedBarcodes.contains(barcode)) return;
    
    _scannedBarcodes.add(barcode);
    debugPrint('扫描到条码: $barcode (总计: ${_scannedBarcodes.length})');
    
    // 发送条码事件
    _barcodeController.add(barcode);
    _countController.add(_scannedBarcodes.length);
    
    // 🔥 异步获取书籍信息
    _fetchBookInfoAsync(barcode);
  }
  
  /// 🔥 借鉴参考项目：异步获取书籍信息（使用SIP2协议）
  void _fetchBookInfoAsync(String barcode) async {
    try {
      // 先发送处理中的结果
      _bookResultController.add(BookScanResult(
        barcode: barcode,
        bookInfo: null,
        scanTime: DateTime.now(),
        status: BookScanStatus.processing,
      ));
      
      // 🔥 优先使用SIP2协议获取书籍信息
      BookInfo? bookInfo = await _sip2BookService.getBookInfo(barcode);

      // 如果SIP2失败，降级使用HTTP API
      if (bookInfo == null) {
        debugPrint('SIP2获取失败，尝试HTTP API: $barcode');
        bookInfo = await _bookInfoService.getBookInfo(barcode);
      }
      
      if (bookInfo != null) {
        debugPrint('获取书籍信息成功: ${bookInfo.bookName} - ${bookInfo.author}');
        
        // 发送成功结果
        _bookResultController.add(BookScanResult(
          barcode: barcode,
          bookInfo: bookInfo,
          scanTime: DateTime.now(),
          status: BookScanStatus.success,
        ));
      } else {
        debugPrint('获取书籍信息失败: $barcode');
        
        // 发送失败结果
        _bookResultController.add(BookScanResult(
          barcode: barcode,
          bookInfo: null,
          scanTime: DateTime.now(),
          status: BookScanStatus.failed,
          error: '获取书籍信息失败',
        ));
      }
    } catch (e) {
      debugPrint('获取书籍信息异常: $barcode, $e');
      
      // 发送错误结果
      _bookResultController.add(BookScanResult(
        barcode: barcode,
        bookInfo: null,
        scanTime: DateTime.now(),
        status: BookScanStatus.failed,
        error: e.toString(),
      ));
    }
  }
  
  /// 开始模拟扫描（实际项目中删除）
  void _startMockScanning() {
    _mockScanTimer = Timer.periodic(const Duration(milliseconds: 1500), (timer) {
      if (!_isScanning) {
        timer.cancel();
        return;
      }
      
      // 随机生成条码
      if (Random().nextDouble() > 0.4) { // 60%概率扫描到书籍
        final barcode = 'BOOK${Random().nextInt(1000).toString().padLeft(3, '0')}';
        _onBarcodeScanned(barcode);
      }
    });
  }
  
  /// 停止扫描
  Future<List<String>> stopScanning() async {
    if (!_isScanning) {
      debugPrint('增强RFID扫描未在进行中');
      return List.from(_scannedBarcodes);
    }
    
    try {
      debugPrint('停止增强RFID扫描');
      
      if (_readerConfigs.isNotEmpty) {
        // 停止真实扫描
        ReaderManager.instance.stopInventory();
        
        // 移除监听器
        final tagProvider = Get.context?.read<HWTagProvider>();
        if (tagProvider != null) {
          tagProvider.removeListener(_onTagDataChanged);
        }
      } else {
        // 停止模拟扫描
        _stopMockScanning();
      }
      
      _isScanning = false;
      
      final result = List<String>.from(_scannedBarcodes);
      debugPrint('增强RFID扫描已停止，共扫描到${result.length}个条码');

      return result;
    } catch (e) {
      final errorMsg = '停止增强RFID扫描失败: $e';
      debugPrint(errorMsg);
      _setError(errorMsg);
      return List<String>.from(_scannedBarcodes);
    }
  }
  
  /// 停止模拟扫描
  void _stopMockScanning() {
    _mockScanTimer?.cancel();
    _mockScanTimer = null;
  }
  
  /// 获取所有已扫描书籍的信息（实时获取）
  Future<List<BookInfo>> getAllScannedBooksInfo() async {
    if (_scannedBarcodes.isEmpty) return [];

    // 实时获取所有书籍信息
    final books = <BookInfo>[];
    for (String barcode in _scannedBarcodes) {
      final bookInfo = await _sip2BookService.getBookInfo(barcode);
      if (bookInfo != null) {
        books.add(bookInfo);
      }
    }
    return books;
  }
  
  /// 获取当前扫描结果
  List<String> getCurrentScanResult() {
    return List<String>.from(_scannedBarcodes);
  }
  
  /// 清空扫描结果
  void clearScanResult() {
    _scannedBarcodes.clear();
    _processedBarcodes.clear();
    _countController.add(0);
    debugPrint('扫描结果已清空');
  }
  
  /// 连接健康检查
  bool _isConnectionHealthy(String cacheKey) {
    final lastUsed = _connectionLastUsed[cacheKey];
    final isHealthy = _connectionHealth[cacheKey] ?? false;
    
    if (!isHealthy || lastUsed == null) return false;
    
    // 连接超过5分钟未使用，认为可能不健康
    return DateTime.now().difference(lastUsed).inMinutes < 5;
  }
  
  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
    _errorController.add(error);
  }
  
  /// 清除错误信息
  void _clearError() {
    _errorMessage = null;
  }
  
  /// 获取服务状态
  Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'scanning': _isScanning,
      'scanned_count': _scannedBarcodes.length,
      'book_info_count': _bookInfoCache.length,
      'reader_configs': _readerConfigs.length,
      'error': _errorMessage,
      'service_name': 'EnhancedRFIDService',
      'version': '1.0.0',
    };
  }
  
  /// 测试连接
  Future<bool> testConnection() async {
    try {
      if (_readerConfigs.isNotEmpty) {
        // 测试真实硬件连接
        ReaderManager.instance.open();
        ReaderManager.instance.close();
        return true;
      } else {
        // 模拟连接测试
        await Future.delayed(const Duration(milliseconds: 100));
        return _isInitialized;
      }
    } catch (e) {
      debugPrint('连接测试失败: $e');
      return false;
    }
  }
  
  /// 重置服务
  Future<void> reset() async {
    debugPrint('重置增强RFID服务');
    
    if (_isScanning) {
      await stopScanning();
    }
    
    clearScanResult();
    _clearError();
  }
  
  /// 释放资源
  void dispose() {
    debugPrint('释放增强RFID服务资源');
    
    _stopMockScanning();
    _isScanning = false;
    _isInitialized = false;
    
    _barcodeController.close();
    _bookResultController.close();
    _countController.close();
    _errorController.close();
    
    debugPrint('增强RFID服务已释放');
  }
}

/// 书籍扫描结果
class BookScanResult {
  final String barcode;
  final BookInfo? bookInfo;
  final DateTime scanTime;
  final BookScanStatus status;
  final String? error;
  
  BookScanResult({
    required this.barcode,
    this.bookInfo,
    required this.scanTime,
    required this.status,
    this.error,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'barcode': barcode,
      'book_info': bookInfo?.toJson(),
      'scan_time': scanTime.toIso8601String(),
      'status': status.toString(),
      'error': error,
    };
  }
}

/// 书籍扫描状态
enum BookScanStatus {
  processing, // 处理中
  success,    // 成功
  failed,     // 失败
}
