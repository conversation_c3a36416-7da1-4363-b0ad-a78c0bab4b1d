import 'package:flutter/material.dart';
import 'dart:async';
import 'package:provider/provider.dart';
import 'package:a3g/core/utils/window_util.dart';
import '../viewmodels/silence_page_viewmodel.dart';
import '../models/gate_command.dart';
import '../services/gate_auth_service.dart';
import '../services/rfid_service.dart';
import 'package:hardware/hardware.dart' as hw;

/// 调试控制面板
class DebugPanel extends StatelessWidget {
  const DebugPanel({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<SilencePageViewModel>(
      builder: (context, viewModel, child) {
        return Positioned(
          top: 100.p,
          right: 30.p,
          child: Container(
            width: 350.p,
            padding: EdgeInsets.all(20.p),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: BorderRadius.circular(15.p),
              border: Border.all(color: Colors.orange, width: 2),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '🔧 调试控制面板',
                  style: TextStyle(
                    color: Colors.orange,
                    fontSize: 18.p,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 15.p),

                // 串口控制
                _buildSectionTitle('串口控制'),
                SizedBox(height: 8.p),
                const SerialControlPanel(),
                SizedBox(height: 15.p),

                // 进馆流程按钮
                _buildSectionTitle('进馆流程'),
                SizedBox(height: 8.p),
                Row(
                  children: [
                    Expanded(
                      child: _buildDebugButton(
                        context,
                        '进馆开始',
                        () => viewModel.simulateSerialCommand(GateCommand.enterStart),
                        Colors.blue,
                      ),
                    ),
                    SizedBox(width: 10.p),
                    Expanded(
                      child: _buildDebugButton(
                        context,
                        '进馆结束',
                        () => viewModel.simulateSerialCommand(GateCommand.enterEnd),
                        Colors.blue,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 15.p),

                // 出馆流程按钮
                _buildSectionTitle('出馆流程'),
                SizedBox(height: 8.p),
                Row(
                  children: [
                    Expanded(
                      child: _buildDebugButton(
                        context,
                        '出馆开始',
                        () => viewModel.simulateSerialCommand(GateCommand.exitStart),
                        Colors.green,
                      ),
                    ),
                    SizedBox(width: 10.p),
                    Expanded(
                      child: _buildDebugButton(
                        context,
                        '到达位置',
                        () => viewModel.simulateSerialCommand(GateCommand.positionReached),
                        Colors.green,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.p),
                _buildDebugButton(
                  context,
                  '出馆结束',
                  () => viewModel.simulateSerialCommand(GateCommand.exitEnd),
                  Colors.green,
                ),

                SizedBox(height: 15.p),

                // RFID扫描控制按钮
                _buildSectionTitle('RFID扫描控制'),
                SizedBox(height: 8.p),
                Row(
                  children: [
                    Expanded(
                      child: _buildDebugButton(
                        context,
                        '开始扫描',
                        () => _startRFIDScanning(context),
                        Colors.purple,
                      ),
                    ),
                    SizedBox(width: 10.p),
                    Expanded(
                      child: _buildDebugButton(
                        context,
                        '停止扫描',
                        () => _stopRFIDScanning(context),
                        Colors.purple,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.p),
                Row(
                  children: [
                    Expanded(
                      child: _buildDebugButton(
                        context,
                        '清空结果',
                        () => _clearRFIDResults(context),
                        Colors.orange,
                      ),
                    ),
                    SizedBox(width: 10.p),
                    Expanded(
                      child: _buildDebugButton(
                        context,
                        '添加测试',
                        () => _addTestBarcode(context),
                        Colors.teal,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 15.p),

                // 系统控制按钮
                _buildSectionTitle('系统控制'),
                SizedBox(height: 8.p),
                _buildDebugButton(
                  context,
                  '重置系统',
                  () => viewModel.resetSystem(),
                  Colors.red,
                ),

                SizedBox(height: 15.p),

                // 当前状态显示
                Container(
                  padding: EdgeInsets.all(15.p),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(8.p),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '系统状态',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.p,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 8.p),
                      _buildStatusItem('闸机状态', viewModel.currentGateState.name),
                      _buildStatusItem('页面状态', viewModel.currentPageState.name),
                      _buildStatusItem('扫描数量', '${viewModel.scannedBarcodes.length}'),
                      _buildStatusItem('书籍信息', '${viewModel.scannedBooksInfo.length}'),
                      _buildRFIDStatusSection(),
                      _buildAuthStatusSection(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Text(
      '$title:',
      style: TextStyle(
        color: Colors.white,
        fontSize: 14.p,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  /// 构建调试按钮
  Widget _buildDebugButton(
    BuildContext context,
    String text,
    VoidCallback onPressed,
    Color color,
  ) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(vertical: 8.p, horizontal: 12.p),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.p),
        ),
        minimumSize: Size(0, 35.p),
      ),
      child: Text(
        text,
        style: TextStyle(fontSize: 12.p),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// 🔥 新增：构建RFID状态监控部分
  Widget _buildRFIDStatusSection() {
    final rfidService = RFIDService.instance;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8.p),
        Text(
          'RFID扫描状态',
          style: TextStyle(
            color: Colors.purple,
            fontSize: 12.p,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 4.p),
        _buildStatusItem('扫描状态', rfidService.isScanning ? '🟢 扫描中' : '🔴 已停止'),
        _buildStatusItem('服务初始化', rfidService.isInitialized ? '✅ 已完成' : '❌ 未完成'),
        _buildStatusItem('扫描计数', '${rfidService.scannedCount}'),
        if (rfidService.scannedCount > 0) ...[
          SizedBox(height: 4.p),
          Text(
            '最近扫描:',
            style: TextStyle(
              color: Colors.purple.shade300,
              fontSize: 10.p,
              fontWeight: FontWeight.bold,
            ),
          ),
          ...rfidService.getCurrentScanResult().take(3).map((barcode) {
            return Padding(
              padding: EdgeInsets.only(left: 8.p, top: 2.p),
              child: Text(
                '• $barcode',
                style: TextStyle(
                  color: Colors.purple.shade200,
                  fontSize: 9.p,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            );
          }).toList(),
        ],
      ],
    );
  }

  /// 构建认证状态监控部分
  Widget _buildAuthStatusSection() {
    final authService = GateAuthService.instance;
    final authStatus = authService.getStatus();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8.p),
        Text(
          '认证服务状态',
          style: TextStyle(
            color: Colors.orange,
            fontSize: 12.p,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 4.p),
        _buildStatusItem('服务状态', authService.isRunning ? '运行中' : '已停止'),
        _buildStatusItem('初始化', authService.isInitialized ? '已完成' : '未完成'),
        if (authStatus['enabled_methods'] != null)
          _buildStatusItem('认证方式', (authStatus['enabled_methods'] as List).join('、')),
        if (authService.errorMessage != null)
          _buildStatusItem('错误信息', authService.errorMessage!),
      ],
    );
  }

  /// 构建状态项
  Widget _buildStatusItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4.p),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 12.p,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.p,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// 🔥 新增：开始RFID扫描
  void _startRFIDScanning(BuildContext context) async {
    final messenger = ScaffoldMessenger.of(context);

    try {
      final rfidService = RFIDService.instance;
      await rfidService.startScanning();

      messenger.showSnackBar(
        const SnackBar(
          content: Text('✅ RFID扫描已启动'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      messenger.showSnackBar(
        SnackBar(
          content: Text('❌ 启动RFID扫描失败: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// 🔥 新增：停止RFID扫描
  void _stopRFIDScanning(BuildContext context) async {
    final messenger = ScaffoldMessenger.of(context);

    try {
      final rfidService = RFIDService.instance;
      final result = await rfidService.stopScanning();

      messenger.showSnackBar(
        SnackBar(
          content: Text('✅ RFID扫描已停止，共扫描到 ${result.length} 个条码'),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      messenger.showSnackBar(
        SnackBar(
          content: Text('❌ 停止RFID扫描失败: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// 🔥 新增：清空RFID扫描结果
  void _clearRFIDResults(BuildContext context) {
    try {
      final rfidService = RFIDService.instance;
      rfidService.clearScanResult();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('✅ RFID扫描结果已清空'),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ 清空扫描结果失败: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// 🔥 新增：添加测试条码
  void _addTestBarcode(BuildContext context) {
    try {
      // 生成测试条码
      final testBarcodes = ['TEST001', 'TEST002', 'TEST003', 'TEST004', 'TEST005'];
      final barcode = testBarcodes[DateTime.now().millisecond % testBarcodes.length];

      // 这里可以通过模拟串口命令来触发扫描
      // 或者直接显示提示信息

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('✅ 测试条码: $barcode (请使用实际RFID设备扫描)'),
          backgroundColor: Colors.teal,
          duration: const Duration(seconds: 3),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ 生成测试条码失败: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
  }

/// 串口控制面板：负责连接、命令发送、事件监听并桥接到ViewModel
class SerialControlPanel extends StatefulWidget {
  const SerialControlPanel({Key? key}) : super(key: key);

  @override
  State<SerialControlPanel> createState() => _SerialControlPanelState();
}

class _SerialControlPanelState extends State<SerialControlPanel> {
  final hw.GateSerialManager _gateSerial = hw.GateSerialManager.instance;
  List<String> _ports = [];
  String? _selectedPort;
  bool _loadingPorts = false;
  StreamSubscription? _gateEventSub;

  @override
  void initState() {
    super.initState();
    _refreshPorts();
    // 监听闸机事件并桥接到 ViewModel
    _gateEventSub = _gateSerial.gateEventStream.listen((event) {
      if (!mounted) return;
      if (event is hw.GateCommandReceivedEvent) {
        _onGateCommandReceived(event.command);
      }
    });
  }

  @override
  void dispose() {
    _gateEventSub?.cancel();
    super.dispose();
  }

  Future<void> _refreshPorts() async {
    setState(() => _loadingPorts = true);
    try {
      final ports = await _gateSerial.getAvailablePorts();
      setState(() {
        _ports = ports;
        if (ports.isNotEmpty) {
          _selectedPort = _selectedPort != null && ports.contains(_selectedPort)
              ? _selectedPort
              : ports.first;
        } else {
          _selectedPort = null;
        }
      });
    } catch (_) {} finally {
      if (mounted) setState(() => _loadingPorts = false);
    }
  }

  Future<void> _connectOrDisconnect() async {
    if (_gateSerial.isConnected) {
      await _gateSerial.disconnectGate();
      if (mounted) setState(() {});
      return;
    }
    if (_selectedPort == null) return;
    final ok = await _gateSerial.connectGate(_selectedPort!);
    if (mounted && ok) setState(() {});
  }

  void _onGateCommandReceived(hw.GateCommand cmd) {
    final viewModel = context.read<SilencePageViewModel>();
    // 将硬件命令映射到本地 GateCommand 字符串常量
    switch (cmd) {
      case hw.GateCommand.enterStart:
        viewModel.simulateSerialCommand(GateCommand.enterStart);
        break;
      case hw.GateCommand.enterEnd:
        viewModel.simulateSerialCommand(GateCommand.enterEnd);
        break;
      case hw.GateCommand.exitStart:
        viewModel.simulateSerialCommand(GateCommand.exitStart);
        break;
      case hw.GateCommand.exitEnd:
        viewModel.simulateSerialCommand(GateCommand.exitEnd);
        break;
      case hw.GateCommand.reachPosition:
        viewModel.simulateSerialCommand(GateCommand.positionReached);
        break;
      case hw.GateCommand.tailgating:
        viewModel.simulateSerialCommand(GateCommand.tailgating);
        break;
      case hw.GateCommand.doorHasPerson:
        viewModel.simulateSerialCommand(GateCommand.doorBlocked);
        break;
      case hw.GateCommand.enterOpen:
      case hw.GateCommand.exitOpen:
      case hw.GateCommand.failSignal:
        // 这些是发送类命令，通常不作为接收事件处理
        break;
    }
  }

  Future<void> _sendEnterOpen() async {
    await _gateSerial.sendEnterOpenCommand();
  }

  Future<void> _sendExitOpen() async {
    await _gateSerial.sendExitOpenCommand();
  }

  Future<void> _sendFailSignal() async {
    await _gateSerial.sendFailSignal();
  }

  @override
  Widget build(BuildContext context) {
    final connected = _gateSerial.isConnected;
    final info = _gateSerial.connectionInfo;

    return Container(
      padding: EdgeInsets.all(12.p),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.25),
        borderRadius: BorderRadius.circular(8.p),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 行1：端口选择与刷新
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedPort,
                  isExpanded: true,
                  items: _ports
                      .map((p) => DropdownMenuItem(value: p, child: Text(p)))
                      .toList(),
                  onChanged: connected
                      ? null
                      : (v) => setState(() => _selectedPort = v),
                  decoration: const InputDecoration(
                    isDense: true,
                    filled: true,
                    fillColor: Colors.black12,
                    labelText: '串口',
                  ),
                ),
              ),
              SizedBox(width: 8.p),
              ElevatedButton(
                onPressed: _loadingPorts ? null : _refreshPorts,
                style: ElevatedButton.styleFrom(backgroundColor: Colors.blueGrey),
                child: Text(_loadingPorts ? '刷新中...' : '刷新端口', style: TextStyle(fontSize: 12.p)),
              ),
            ],
          ),
          SizedBox(height: 8.p),
          // 行2：连接/断开 与 状态
          Row(
            children: [
              ElevatedButton(
                onPressed: _connectOrDisconnect,
                style: ElevatedButton.styleFrom(
                  backgroundColor: connected ? Colors.redAccent : Colors.green,
                ),
                child: Text(connected ? '断开' : '连接', style: TextStyle(fontSize: 12.p)),
              ),
              SizedBox(width: 10.p),
              Expanded(
                child: Text(
                  connected
                      ? '已连接 ${info['portName'] ?? ''} @ ${info['baudRate'] ?? ''}'
                      : '未连接',
                  style: TextStyle(color: Colors.white70, fontSize: 12.p),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.p),
          // 行3：发送命令按钮
          Row(
            children: [
              Expanded(
                child: _buildSendButton('进馆开门', _sendEnterOpen, Colors.teal),
              ),
              SizedBox(width: 8.p),
              Expanded(
                child: _buildSendButton('出馆开门', _sendExitOpen, Colors.teal),
              ),
              SizedBox(width: 8.p),
              Expanded(
                child: _buildSendButton('失败信号', _sendFailSignal, Colors.deepOrange),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSendButton(String text, VoidCallback onPressed, Color color) {
    return ElevatedButton(
      onPressed: _gateSerial.isConnected ? onPressed : null,
      style: ElevatedButton.styleFrom(backgroundColor: color),
      child: Text(text, style: TextStyle(fontSize: 12.p, color: Colors.white)),
    );
  }
}


