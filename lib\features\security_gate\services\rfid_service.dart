import 'dart:async';
import 'package:flutter/foundation.dart';

import '../models/book_info.dart';
import 'book_info_api_service.dart';
import 'enhanced_rfid_service.dart';

/// RFID扫描服务
/// 🔥 增强版：集成书籍信息获取功能
class RFIDService {
  static RFIDService? _instance;
  static RFIDService get instance => _instance ??= RFIDService._();
  RFIDService._();

  // 🔥 新增：增强RFID服务
  final EnhancedRFIDService _enhancedService = EnhancedRFIDService.instance;

  // 🔥 新增：书籍信息API服务
  final BookInfoApiService _bookInfoService = BookInfoApiService.instance;

  // 扫描状态
  bool _isScanning = false;
  bool _isInitialized = false;

  // 扫描结果
  final List<String> _scannedBarcodes = [];

  // 🚫 移除缓存：书籍信息需要实时获取，确保借阅状态准确性

  // 事件流
  final StreamController<String> _barcodeController =
      StreamController<String>.broadcast();
  Stream<String> get barcodeStream => _barcodeController.stream;

  final StreamController<int> _countController =
      StreamController<int>.broadcast();
  Stream<int> get countStream => _countController.stream;

  final StreamController<String> _errorController =
      StreamController<String>.broadcast();
  Stream<String> get errorStream => _errorController.stream;

  // 🔥 新增：书籍扫描结果流
  final StreamController<BookScanResult> _bookResultController =
      StreamController<BookScanResult>.broadcast();
  Stream<BookScanResult> get bookResultStream => _bookResultController.stream;


  
  /// 🔥 增强：初始化RFID服务（集成书籍信息获取）
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('RFID服务已经初始化');
      return;
    }

    try {
      debugPrint('开始初始化增强RFID服务...');

      // 🔥 初始化增强RFID服务
      await _enhancedService.initialize();

      // 🔥 初始化书籍API服务
      _bookInfoService.initialize();

      // 🔥 监听增强服务的书籍扫描结果
      _enhancedService.bookResultStream.listen((result) {
        _onBookScanResult(result);
      });

      // 🔥 监听增强服务的条码流
      _enhancedService.barcodeStream.listen((barcode) {
        _barcodeController.add(barcode);
      });

      // 🔥 监听增强服务的计数流
      _enhancedService.countStream.listen((count) {
        _countController.add(count);
      });

      _isInitialized = true;
      debugPrint('增强RFID服务初始化完成');
    } catch (e) {
      final errorMsg = '增强RFID服务初始化失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }

  /// 🔥 新增：处理书籍扫描结果
  void _onBookScanResult(BookScanResult result) {
    // 转发书籍扫描结果（不再缓存，确保数据实时性）
    _bookResultController.add(result);

    debugPrint('书籍扫描结果: ${result.barcode} - ${result.status} - ${result.bookInfo?.bookName ?? "未知"}');
  }

  /// 🔥 增强：开始扫描（集成书籍信息获取）
  Future<void> startScanning() async {
    if (!_isInitialized) {
      throw Exception('RFID服务未初始化');
    }

    if (_isScanning) {
      debugPrint('增强RFID扫描已在进行中');
      return;
    }

    try {
      debugPrint('开始增强RFID扫描');

      // 清空之前的扫描结果
      _scannedBarcodes.clear();

      // 🔥 使用增强RFID服务开始扫描
      await _enhancedService.startEnhancedScanning();

      _isScanning = true;

      debugPrint('增强RFID扫描已启动');
    } catch (e) {
      final errorMsg = '启动增强RFID扫描失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }
  
  /// 🔥 增强：停止扫描
  Future<List<String>> stopScanning() async {
    if (!_isScanning) {
      debugPrint('增强RFID扫描未在进行中');
      return List.from(_scannedBarcodes);
    }

    try {
      debugPrint('停止增强RFID扫描');

      // 🔥 使用增强RFID服务停止扫描
      final result = await _enhancedService.stopScanning();

      _isScanning = false;

      debugPrint('增强RFID扫描已停止，共扫描到${result.length}个条码');

      return result;
    } catch (e) {
      final errorMsg = '停止增强RFID扫描失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      return List<String>.from(_scannedBarcodes);
    }
  }
  
  /// 获取当前扫描结果
  List<String> getCurrentScanResult() {
    return List<String>.from(_scannedBarcodes);
  }

  /// 🔥 新增：获取所有已扫描书籍的详细信息（实时获取）
  Future<List<BookInfo>> getAllScannedBooksInfo() async {
    return await _enhancedService.getAllScannedBooksInfo();
  }

  /// 🚫 缓存已移除：书籍信息需要实时获取
  @Deprecated('缓存已移除，请使用getAllScannedBooksInfo()获取实时数据')
  Map<String, BookInfo> get bookInfoCache => {};

  /// 获取扫描数量
  int get scannedCount => _scannedBarcodes.length;
  
  /// 是否正在扫描
  bool get isScanning => _isScanning;
  
  /// 是否已初始化
  bool get isInitialized => _isInitialized;
  
  /// 🔥 增强：清空扫描结果
  void clearScanResult() {
    _scannedBarcodes.clear();
    _enhancedService.clearScanResult();
    _countController.add(0);
  }
  
  /// 手动添加条码（用于测试）
  void addBarcode(String barcode) {
    if (!_scannedBarcodes.contains(barcode)) {
      _scannedBarcodes.add(barcode);
      _barcodeController.add(barcode);
      _countController.add(_scannedBarcodes.length);
      debugPrint('手动添加条码: $barcode');
    }
  }
  

  
  /// 获取服务状态
  Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'scanning': _isScanning,
      'scanned_count': _scannedBarcodes.length,
      'scanned_barcodes': List.from(_scannedBarcodes),
      'service_name': 'RFIDService',
      'version': '1.0.0',
    };
  }
  
  /// 🔥 增强：测试RFID设备连接
  Future<bool> testConnection() async {
    try {
      // 🔥 使用增强RFID服务测试连接
      return await _enhancedService.testConnection();
    } catch (e) {
      debugPrint('增强RFID设备连接测试失败: $e');
      return false;
    }
  }
  
  /// 🔥 增强：重置服务
  Future<void> reset() async {
    debugPrint('重置增强RFID服务');

    if (_isScanning) {
      await stopScanning();
    }

    _scannedBarcodes.clear();
    await _enhancedService.reset();
    _countController.add(0);
  }
  
  /// 🔥 增强：释放资源
  void dispose() {
    debugPrint('释放增强RFID服务资源');

    _isScanning = false;
    _isInitialized = false;

    _barcodeController.close();
    _countController.close();
    _errorController.close();
    _bookResultController.close();

    _enhancedService.dispose();

    debugPrint('增强RFID服务已释放');
  }
}
