2025-08-11 09:50:10.048988: 61
2025-08-11 09:50:10.476299: 开始初始化闸机协调器...
2025-08-11 09:50:10.476299: 可用串口: [COM1, COM4, COM3]
2025-08-11 09:50:10.482257: 连接闸机串口: COM1
2025-08-11 09:50:10.482257: 尝试连接串口: COM1, 波特率: 9600
2025-08-11 09:50:10.482257: 串口连接成功: COM1 at 9600 baud
2025-08-11 09:50:10.483254: 开始监听串口数据
2025-08-11 09:50:10.483254: 串口连接状态变化: true
2025-08-11 09:50:10.483254: 闸机串口连接成功
2025-08-11 09:50:10.484252: 串口 COM1 连接成功 (波特率: 9600)
2025-08-11 09:50:10.484898: 闸机串口服务初始化成功: COM1
2025-08-11 09:50:10.484898: 开始监听串口数据（通过 GateSerialManager 事件流）
2025-08-11 09:50:10.484898: 开始监听闸机串口命令
2025-08-11 09:50:10.484898: 闸机协调器初始化完成
2025-08-11 09:50:10.484898: 安全闸机系统初始化完成
2025-08-11 09:50:10.485898: socket 连接成功,isBroadcast:false
2025-08-11 09:50:10.485898: changeSocketStatus:true
2025-08-11 09:50:10.485898: Sip2HeartBeatManager start loginACS:false askACS:false
2025-08-11 09:50:10.485898: Req msgType：Sip2MsgType.login ,length:72， ret:  9300CNhlsp_sip2|COsip2|CP3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AY1AZEC16
2025-08-11 09:50:10.503281: 开始初始化MultiAuthManager...
2025-08-11 09:50:10.503281: 多认证管理器状态变更: initializing
2025-08-11 09:50:10.503281: 认证优先级管理器: 开始加载认证方式
2025-08-11 09:50:10.503281: 配置的排序: [人脸识别认证, 微信扫码认证, 读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 社保卡认证, 市民卡认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 微信二维码认证, 上海随申码认证, 汇文二维码, 支付宝二维码认证, 支付宝二维码认证（阿里信用）]
2025-08-11 09:50:10.503281: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-11 09:50:10.503281: 认证优先级管理器: 按配置顺序添加 微信扫码认证 -> 微信扫码
2025-08-11 09:50:10.503281: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-08-11 09:50:10.503281: 认证优先级管理器: 按配置顺序添加 社保卡认证 -> 社保卡
2025-08-11 09:50:10.503281: 认证优先级管理器: 按配置顺序添加 电子社保卡认证 -> 电子社保卡
2025-08-11 09:50:10.504269: 认证优先级管理器: 最终排序结果: 微信扫码 -> 读者证 -> 社保卡 -> 电子社保卡
2025-08-11 09:50:10.504269: 认证优先级管理器: 主要认证方式: 微信扫码
2025-08-11 09:50:10.504269: 多认证管理器: 从优先级管理器加载的认证方式: 微信扫码 -> 读者证 -> 社保卡 -> 电子社保卡
2025-08-11 09:50:10.504269: 多认证管理器: 当前默认显示方式: 微信扫码
2025-08-11 09:50:10.504269: 初始化二维码扫描认证服务
2025-08-11 09:50:10.504269: 初始化二维码扫码器
2025-08-11 09:50:10.504269: 二维码扫码器初始化完成
2025-08-11 09:50:10.505258: 二维码扫描认证服务初始化成功
2025-08-11 09:50:10.505258: 初始化共享二维码扫描认证服务
2025-08-11 09:50:10.505258: 微信扫码 认证服务初始化成功
2025-08-11 09:50:10.505258: 初始化读卡器认证服务
2025-08-11 09:50:10.505258: 读卡器认证服务初始化成功
2025-08-11 09:50:10.505258: 初始化共享读卡器认证服务
2025-08-11 09:50:10.506250: 读者证 认证服务初始化成功
2025-08-11 09:50:10.506250: 社保卡 认证服务初始化成功
2025-08-11 09:50:10.507249: 电子社保卡 认证服务初始化成功
2025-08-11 09:50:10.507249: 认证服务初始化完成，共初始化 4 种认证方式
2025-08-11 09:50:10.507249: 多认证管理器状态变更: idle
2025-08-11 09:50:10.507249: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-11 09:50:10.507249: MultiAuthManager初始化完成
2025-08-11 09:50:10.507249: 开始初始化SilencePageViewModel...
2025-08-11 09:50:10.507249: 闸机串口服务已经初始化
2025-08-11 09:50:10.507249: 开始初始化闸机认证服务...
2025-08-11 09:50:10.507249: 闸机认证服务初始化完成，启用认证方式: 人脸识别、读者证、AuthMethod.wechatScanQRCode
2025-08-11 09:50:10.508247: 开始初始化增强RFID服务...
2025-08-11 09:50:10.508247: 开始初始化增强RFID服务...
2025-08-11 09:50:10.508247: 书籍API服务初始化完成: http://your-api-server.com
2025-08-11 09:50:10.508247: SIP2图书信息服务初始化完成
2025-08-11 09:50:10.508247: 增强RFID服务初始化完成，配置了1个阅读器
2025-08-11 09:50:10.508247: 书籍API服务初始化完成: http://your-api-server.com
2025-08-11 09:50:10.508247: 增强RFID服务初始化完成
2025-08-11 09:50:10.508247: 书籍API服务初始化完成: http://api.library.com
2025-08-11 09:50:10.509243: 串口监听已经启动
2025-08-11 09:50:10.509243: SilencePageViewModel初始化完成
2025-08-11 09:50:10.862391: dispose IndexPage
2025-08-11 09:50:10.862391: IndexPage dispose
2025-08-11 09:50:10.931208: Rsp : 941AY1AZFDFC
2025-08-11 09:50:10.947165: loginRsp:{OK: 1, MsgSeqId: 1AZFDFC}
2025-08-11 09:50:10.947165: Sip2HeartBeatManager start loginACS:false askACS:true
2025-08-11 09:50:10.947165: 发送心跳
2025-08-11 09:50:10.947165: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY2AZFC9F
2025-08-11 09:50:10.965117: Rsp : 98YYYNNN00500320250811    0951542.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY2AZD51F
2025-08-11 09:50:16.757123: 收到串口命令: enter_start (进馆开始)
2025-08-11 09:50:16.757123: 处理进馆开始
2025-08-11 09:50:16.757123: 闸机状态变更: GateState.enterStarted
2025-08-11 09:50:16.757123: 页面状态变更: SilencePageState.authenticating
2025-08-11 09:50:16.758124: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-11 09:50:16.758124: 启动闸机认证服务...
2025-08-11 09:50:16.758124: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-11 09:50:16.758124: 开始启动MultiAuthManager监听...
2025-08-11 09:50:16.758124: 认证状态变化: MultiAuthState.listening
2025-08-11 09:50:16.758124: 认证状态变化: MultiAuthState.listening
2025-08-11 09:50:16.758124: 多认证管理器状态变更: listening
2025-08-11 09:50:16.758124: 启动所有认证方式监听: [AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-11 09:50:16.758124: 准备启动 2 个物理认证服务
2025-08-11 09:50:16.758124: 开始二维码扫描认证监听
2025-08-11 09:50:16.758124: 开始二维码扫描监听
2025-08-11 09:50:16.759122: 二维码扫描认证监听启动成功
2025-08-11 09:50:16.759122: 微信扫码 认证服务启动成功
2025-08-11 09:50:16.759122: 开始读卡器认证监听
2025-08-11 09:50:16.760062: 强制重新配置读卡器以确保状态一致性
2025-08-11 09:50:16.760062: 完全重置读卡器连接和监听器状态...
2025-08-11 09:50:16.760062: 已移除读卡器状态监听器
2025-08-11 09:50:16.760062: 已移除标签数据监听器
2025-08-11 09:50:16.760062: 所有卡片监听器已移除
2025-08-11 09:50:16.760062: stopInventory newPort:null
2025-08-11 09:50:16.962427: 发送 关闭 阅读器newPort:null
2025-08-11 09:50:16.963424: 读卡器连接已完全关闭
2025-08-11 09:50:16.963424: 读卡器连接和监听器状态已完全重置
2025-08-11 09:50:16.963424: 添加设备配置: 读者证认证 -> 1个设备
2025-08-11 09:50:16.963424: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-11 09:50:16.963424: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-11 09:50:16.964421: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-11 09:50:16.964421: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-11 09:50:16.964421: 添加有效设备: type=10, id=10
2025-08-11 09:50:16.964421: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-11 09:50:16.964421: 添加有效设备: type=13, id=13
2025-08-11 09:50:16.964421: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-11 09:50:16.964421: 添加有效设备: type=12, id=12
2025-08-11 09:50:16.964421: 总共加载了3个设备配置
2025-08-11 09:50:16.965419: changeReaders
2025-08-11 09:50:16.965419: createIsolate isOpen:false,isOpening:false
2025-08-11 09:50:16.965419: createIsolate newport null
2025-08-11 09:50:17.467953: open():SendPort
2025-08-11 09:50:17.467953: untilDetcted():SendPort
2025-08-11 09:50:17.468954: 读卡器配置完成，共 3 个设备
2025-08-11 09:50:17.468954: 已移除读卡器状态监听器
2025-08-11 09:50:17.468954: 已移除标签数据监听器
2025-08-11 09:50:17.468954: 所有卡片监听器已移除
2025-08-11 09:50:17.468954: 已添加读卡器状态监听器
2025-08-11 09:50:17.468954: 已添加标签数据监听器
2025-08-11 09:50:17.468954: 开始监听卡片数据 - 所有监听器已就绪
2025-08-11 09:50:17.468954: 读卡器认证监听启动成功
2025-08-11 09:50:17.468954: 读者证、社保卡、电子社保卡 认证服务启动成功
2025-08-11 09:50:17.469950: 所有认证服务启动完成，成功启动 2 个服务
2025-08-11 09:50:17.469950: 当前可用的认证方式: 微信扫码、读者证、社保卡、电子社保卡
2025-08-11 09:50:17.469950: MultiAuthManager启动监听成功
2025-08-11 09:50:17.469950: 闸机认证服务启动成功（无UI模式），超时时间: 30秒
2025-08-11 09:50:17.469950: 闸机状态变更: GateState.enterScanning
2025-08-11 09:50:17.469950: 闸机认证服务启动成功
2025-08-11 09:50:17.580567: subThread :ReaderCommand.readerList
2025-08-11 09:50:17.580567: commandRsp:ReaderCommand.readerList
2025-08-11 09:50:17.581570: readerList：3,readerSetting：3
2025-08-11 09:50:17.581570: cacheUsedReaders:3
2025-08-11 09:50:17.581570: subThread :ReaderCommand.open
2025-08-11 09:50:17.581570: commandRsp:ReaderCommand.open
2025-08-11 09:50:17.756815: dc_init:0xb4 100
2025-08-11 09:50:17.756815: open reader readerType ：10 ret：0
2025-08-11 09:50:17.756815: open reader readerType ：13 ret：0
2025-08-11 09:50:17.768979: 去开启 读 监听
2025-08-11 09:50:17.768979: widget.port.isOpen:true
2025-08-11 09:50:17.771783: 打开COM4读写成功
2025-08-11 09:50:17.771783: 发送：[16, 54, 0d]
2025-08-11 09:50:17.771783: open reader readerType ：12 ret：0
2025-08-11 09:50:17.772782: [[10, 0], [13, 0], [12, 0]]
2025-08-11 09:50:17.772782: changeType:ReaderErrorType.openSuccess
2025-08-11 09:50:17.772782: 读卡器状态变化: ReaderErrorType.openSuccess
2025-08-11 09:50:17.772782: 读卡器连接成功
2025-08-11 09:50:17.772782: 读卡器连接成功，确保扫描状态正常
2025-08-11 09:50:17.773781: 恢复读卡器扫描状态...
2025-08-11 09:50:17.773781: 读卡器扫描状态已恢复
2025-08-11 09:50:17.774809: subThread :ReaderCommand.untilDetected
2025-08-11 09:50:17.774809: commandRsp:ReaderCommand.untilDetected
2025-08-11 09:50:17.774809: subThread :ReaderCommand.resumeInventory
2025-08-11 09:50:17.775801: commandRsp:ReaderCommand.resumeInventory
2025-08-11 09:50:18.300357: dc_config_card:0
2025-08-11 09:50:18.324191: dc_card_n_hex:65534,len:0
2025-08-11 09:50:18.324191: iReadCardBas: start ,isOpenComPort:false
2025-08-11 09:50:18.392464: iReadCardBas ret:4294967294
2025-08-11 09:50:18.396457: 无卡
2025-08-11 09:50:18.396457: 无卡
2025-08-11 09:50:18.779375: dc_config_card:0
2025-08-11 09:50:18.796284: dc_card_n_hex:1,len:0
2025-08-11 09:50:18.796284: iReadCardBas: start ,isOpenComPort:false
2025-08-11 09:50:18.863972: iReadCardBas ret:4294967294
2025-08-11 09:50:18.863972: 无卡
2025-08-11 09:50:18.864692: 无卡
2025-08-11 09:50:19.276147: dc_config_card:0
2025-08-11 09:50:19.292199: dc_card_n_hex:1,len:0
2025-08-11 09:50:19.292199: iReadCardBas: start ,isOpenComPort:false
2025-08-11 09:50:19.359743: iReadCardBas ret:4294967294
2025-08-11 09:50:19.359743: 无卡
2025-08-11 09:50:19.359743: 无卡
2025-08-11 09:50:19.780008: dc_config_card:0
2025-08-11 09:50:19.796228: dc_card_n_hex:1,len:0
2025-08-11 09:50:19.796228: iReadCardBas: start ,isOpenComPort:false
2025-08-11 09:50:19.863790: iReadCardBas ret:4294967294
2025-08-11 09:50:19.863790: 无卡
2025-08-11 09:50:19.863790: 无卡
2025-08-11 09:50:20.276084: dc_config_card:0
2025-08-11 09:50:20.292243: dc_card_n_hex:1,len:0
2025-08-11 09:50:20.292243: iReadCardBas: start ,isOpenComPort:false
2025-08-11 09:50:20.359440: iReadCardBas ret:4294967294
2025-08-11 09:50:20.359440: 无卡
2025-08-11 09:50:20.359440: 无卡
2025-08-11 09:50:20.780274: dc_config_card:0
2025-08-11 09:50:20.868358: dc_card_n_hex:0,len:4
2025-08-11 09:50:20.868358: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-11 09:50:20.932287: dc_authentication_pass:0
2025-08-11 09:50:20.939617: dc_read:0
2025-08-11 09:50:20.939617: data:31313131313100000000000000000000,coder:Std14443ACoder
2025-08-11 09:50:20.940621: parseRet：{"barCode":"111111"},decoder:14443AStd
2025-08-11 09:50:20.940621: 🎯 HWTagProvider.changeAddedItem: 收到 1 个新标签
2025-08-11 09:50:20.941274: 🏷️ 新标签[0]: uid=A577EB2D, barCode=111111, readerType=10
2025-08-11 09:50:20.941274: 🎯 HWTagProvider: 当前总标签数 1, 新增标签数 1
2025-08-11 09:50:20.942274: 检测到二维码数据: 111111
2025-08-11 09:50:20.942274: 未知的二维码类型: 111111
2025-08-11 09:50:20.942274: 检测到读卡器数据: 1条
2025-08-11 09:50:20.942274: 读卡器数据认证：
2025-08-11 09:50:20.942274:   设备类型: 10
2025-08-11 09:50:20.942274:   条码: 111111
2025-08-11 09:50:20.942274:   标签UID: A577EB2D
2025-08-11 09:50:20.942274:   对应登录类型: AuthLoginType.readerCard
2025-08-11 09:50:20.942274:   根据读卡器类型10确定认证方式为: 读者证
2025-08-11 09:50:20.942274:   开始调用认证API: 111111
2025-08-11 09:50:20.942274: 正在认证用户: 111111, 方式: 读者证
2025-08-11 09:50:20.943303: 多认证管理器: 切换显示方式 微信扫码 -> 读者证
2025-08-11 09:50:20.943303: 认证状态变化: MultiAuthState.listening
2025-08-11 09:50:20.943303: 认证状态变化: MultiAuthState.listening
2025-08-11 09:50:20.943303: 多认证管理器: 读者证获得认证请求锁
2025-08-11 09:50:20.943303: 63 CardType 值为空
2025-08-11 09:50:20.943303: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250811    095020  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY3AZF021
2025-08-11 09:50:20.943303: iReadCardBas: start ,isOpenComPort:false
2025-08-11 09:50:21.006853: iReadCardBas ret:4294967294
2025-08-11 09:50:21.006853: 无卡
2025-08-11 09:50:21.006853: 无卡
2025-08-11 09:50:21.007858: 检测到了卡 来暂停 ：1
2025-08-11 09:50:27.145249: Rsp : 64              00120250811    095210000100010000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|AS10006811|JF0.0|BE|AF|AG|AY3AZDBBF
2025-08-11 09:50:27.160098: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250811    095210, HoldItemsCount: 0001, OverdueItemCount: 0001, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 3AZDBBF}
2025-08-11 09:50:27.160098: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250811    095210, HoldItemsCount: 0001, OverdueItemCount: 0001, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 3AZDBBF}
2025-08-11 09:50:27.160098: SIP2认证成功: 用户=gd, ID=111111
2025-08-11 09:50:27.161107:   认证结果: AuthStatus.success, 方式: 读者证
2025-08-11 09:50:27.161107: 认证成功，继续监听下一个用户
2025-08-11 09:50:27.161107: 恢复读卡器扫描状态...
2025-08-11 09:50:27.161107: 读卡器扫描状态已恢复
2025-08-11 09:50:27.162095: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-11 09:50:27.162095: 认证状态变化: MultiAuthState.authenticating
2025-08-11 09:50:27.162095: 认证状态变化: MultiAuthState.authenticating
2025-08-11 09:50:27.162095: 多认证管理器状态变更: authenticating
2025-08-11 09:50:27.162095: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-11 09:50:27.162095: 认证状态变化: MultiAuthState.completed
2025-08-11 09:50:27.162095: 认证状态变化: MultiAuthState.completed
2025-08-11 09:50:27.162095: 多认证管理器状态变更: completed
2025-08-11 09:50:27.163101: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-11 09:50:27.163101: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-11 09:50:27.163101: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-11 09:50:27.163101: 认证状态变化: MultiAuthState.authenticating
2025-08-11 09:50:27.163101: 认证状态变化: MultiAuthState.authenticating
2025-08-11 09:50:27.163101: 多认证管理器状态变更: authenticating
2025-08-11 09:50:27.163101: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-11 09:50:27.163101: 认证状态变化: MultiAuthState.completed
2025-08-11 09:50:27.163101: 认证状态变化: MultiAuthState.completed
2025-08-11 09:50:27.164090: 多认证管理器状态变更: completed
2025-08-11 09:50:27.164090: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-11 09:50:27.164090: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-11 09:50:27.164090: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-11 09:50:27.164090: 认证状态变化: MultiAuthState.authenticating
2025-08-11 09:50:27.164090: 认证状态变化: MultiAuthState.authenticating
2025-08-11 09:50:27.164090: 多认证管理器状态变更: authenticating
2025-08-11 09:50:27.164090: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-11 09:50:27.164090: 认证状态变化: MultiAuthState.completed
2025-08-11 09:50:27.164090: 认证状态变化: MultiAuthState.completed
2025-08-11 09:50:27.164090: 多认证管理器状态变更: completed
2025-08-11 09:50:27.164090: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-11 09:50:27.165087: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-11 09:50:27.165087: 非认证状态收到认证结果，忽略
2025-08-11 09:50:27.165087: 收到认证结果: AuthStatus.success, 用户: gd
2025-08-11 09:50:27.165087: 收到闸机认证结果: AuthStatus.success, 用户: gd
2025-08-11 09:50:27.165087: 闸机状态变更: GateState.enterOpening
2025-08-11 09:50:27.165087: 页面状态变更: SilencePageState.authSuccess
2025-08-11 09:50:27.165087: 发送闸机命令: GateCommand.enterOpen, 数据: aa 00 02 01 00 00 48 72
2025-08-11 09:50:27.165087: 发送数据失败: 期望发送8字节，实际发送7字节
2025-08-11 09:50:27.165087: 非认证状态收到认证结果，忽略
2025-08-11 09:50:27.166084: 串口错误: SEND_INCOMPLETE - 发送数据不完整: 期望8字节，实际7字节
2025-08-11 09:50:27.166084: 收到认证结果: AuthStatus.success, 用户: gd
2025-08-11 09:50:27.166084: 非认证状态收到认证结果，忽略
2025-08-11 09:50:27.166084: 非认证状态收到认证结果，忽略
2025-08-11 09:50:27.166084: 收到认证结果: AuthStatus.success, 用户: gd
2025-08-11 09:50:27.166084: 非认证状态收到认证结果，忽略
2025-08-11 09:50:27.167094: subThread :ReaderCommand.resumeInventory
2025-08-11 09:50:27.167094: commandRsp:ReaderCommand.resumeInventory
2025-08-11 09:50:27.276089: dc_config_card:0
2025-08-11 09:50:27.372225: dc_card_n_hex:0,len:4
2025-08-11 09:50:27.372225: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-11 09:50:27.380185: dc_authentication_pass:0
2025-08-11 09:50:27.387675: dc_read:0
2025-08-11 09:50:27.387675: iReadCardBas: start ,isOpenComPort:false
2025-08-11 09:50:27.550167: iReadCardBas ret:4294967294
2025-08-11 09:50:27.550167: 无卡
2025-08-11 09:50:27.550973: 无卡
2025-08-11 09:50:27.550973: 检测到了卡 来暂停 ：1
2025-08-11 09:50:27.550973: 检测到二维码数据: 111111
2025-08-11 09:50:27.550973: 未知的二维码类型: 111111
2025-08-11 09:50:30.165813: 停止闸机认证服务...
2025-08-11 09:50:30.165813: 闸机状态变更: GateState.idle
2025-08-11 09:50:30.165813: 页面状态变更: SilencePageState.welcome
2025-08-11 09:50:30.165813: 认证监听未在运行中
2025-08-11 09:50:30.165813: 闸机认证服务已停止
2025-08-11 09:50:32.164457: 多认证管理器: 认证请求锁已释放（之前为读者证）
2025-08-11 09:50:32.164457: 多认证管理器: 还原到默认显示方式: 微信扫码
2025-08-11 09:50:35.163367: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-08-11 09:50:35.163718: 多认证管理器状态变更: listening
2025-08-11 09:50:35.163718: ⚠️ 人脸识别服务未初始化或不可用
2025-08-11 09:50:36.432184: 收到串口命令: enter_end (进馆结束)
2025-08-11 09:50:36.433480: 处理进馆结束
2025-08-11 09:50:36.433480: 闸机认证服务未在运行
2025-08-11 09:50:36.433480: 闸机状态变更: GateState.enterOver
2025-08-11 09:50:37.435486: 闸机认证服务未在运行
2025-08-11 09:50:37.435486: 闸机状态变更: GateState.idle
2025-08-11 09:50:37.435486: 页面状态变更: SilencePageState.welcome
2025-08-11 09:50:40.480750: 收到串口命令: enter_start (进馆开始)
2025-08-11 09:50:40.480750: 处理进馆开始
2025-08-11 09:50:40.481750: 闸机状态变更: GateState.enterStarted
2025-08-11 09:50:40.481750: 页面状态变更: SilencePageState.authenticating
2025-08-11 09:50:40.481750: MultiAuthManager当前状态: MultiAuthState.listening
2025-08-11 09:50:40.481750: 启动闸机认证服务...
2025-08-11 09:50:40.481750: MultiAuthManager当前状态: MultiAuthState.listening
2025-08-11 09:50:40.481750: 开始启动MultiAuthManager监听...
2025-08-11 09:50:40.481750: 认证监听已在运行中
2025-08-11 09:50:40.481750: MultiAuthManager启动监听成功
2025-08-11 09:50:40.482747: 闸机认证服务启动成功（无UI模式），超时时间: 30秒
2025-08-11 09:50:40.482747: 闸机状态变更: GateState.enterScanning
2025-08-11 09:50:40.482747: 闸机认证服务启动成功
2025-08-11 09:51:10.483458: 闸机认证超时
2025-08-11 09:51:10.483458: 收到认证结果: AuthStatus.failureTimeout, 用户: null
2025-08-11 09:51:10.483458: 收到闸机认证结果: AuthStatus.failureTimeout, 用户: null
2025-08-11 09:51:10.484458: 页面状态变更: SilencePageState.authFailed
2025-08-11 09:51:10.982369: 发送心跳
2025-08-11 09:51:10.982369: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY4AZFC9D
2025-08-11 09:51:11.002595: Rsp : 98YYYNNN00500320250811    0952542.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY4AZD51C
2025-08-11 09:51:15.485064: 停止闸机认证服务...
2025-08-11 09:51:15.485064: 闸机状态变更: GateState.idle
2025-08-11 09:51:15.486065: 页面状态变更: SilencePageState.welcome
2025-08-11 09:51:15.486065: 停止所有认证方式监听
2025-08-11 09:51:15.486065: 停止微信扫码认证监听
2025-08-11 09:51:15.486065: 停止二维码扫描认证监听
2025-08-11 09:51:15.486065: 停止读者证认证监听
2025-08-11 09:51:15.486065: 停止读卡器认证监听
2025-08-11 09:51:15.486065: 停止社保卡认证监听
2025-08-11 09:51:15.487061: 停止读卡器认证监听
2025-08-11 09:51:15.487061: 停止电子社保卡认证监听
2025-08-11 09:51:15.487603: 停止读卡器认证监听
2025-08-11 09:51:15.487603: 二维码扫描认证监听已停止
2025-08-11 09:51:15.487603: 微信扫码认证服务监听已停止
2025-08-11 09:51:15.487603: 已移除读卡器状态监听器
2025-08-11 09:51:15.488611: 已移除标签数据监听器
2025-08-11 09:51:15.488611: 所有卡片监听器已移除
2025-08-11 09:51:15.488611: 暂停读卡器扫描（保持连接）...
2025-08-11 09:51:15.488611: stopInventory newPort:SendPort
2025-08-11 09:51:15.488611: 已移除读卡器状态监听器
2025-08-11 09:51:15.488611: 已移除标签数据监听器
2025-08-11 09:51:15.488611: 所有卡片监听器已移除
2025-08-11 09:51:15.488611: 暂停读卡器扫描（保持连接）...
2025-08-11 09:51:15.488611: stopInventory newPort:SendPort
2025-08-11 09:51:15.488611: 已移除读卡器状态监听器
2025-08-11 09:51:15.488611: 已移除标签数据监听器
2025-08-11 09:51:15.489600: 所有卡片监听器已移除
2025-08-11 09:51:15.489600: 暂停读卡器扫描（保持连接）...
2025-08-11 09:51:15.489600: stopInventory newPort:SendPort
2025-08-11 09:51:15.489600: subThread :ReaderCommand.stopInventory
2025-08-11 09:51:15.489600: commandRsp:ReaderCommand.stopInventory
2025-08-11 09:51:15.489600: subThread :ReaderCommand.stopInventory
2025-08-11 09:51:15.490598: commandRsp:ReaderCommand.stopInventory
2025-08-11 09:51:15.490598: subThread :ReaderCommand.stopInventory
2025-08-11 09:51:15.490598: commandRsp:ReaderCommand.stopInventory
2025-08-11 09:51:15.589862: 读卡器扫描已暂停，USB/串口连接保持
2025-08-11 09:51:15.589862: 读卡器认证监听已停止（连接保持）
2025-08-11 09:51:15.589862: 读者证认证服务监听已停止
2025-08-11 09:51:15.590878: 读卡器扫描已暂停，USB/串口连接保持
2025-08-11 09:51:15.590878: 读卡器认证监听已停止（连接保持）
2025-08-11 09:51:15.590878: 社保卡认证服务监听已停止
2025-08-11 09:51:15.590878: 读卡器扫描已暂停，USB/串口连接保持
2025-08-11 09:51:15.590878: 读卡器认证监听已停止（连接保持）
2025-08-11 09:51:15.590878: 电子社保卡认证服务监听已停止
2025-08-11 09:51:15.591888: 多认证管理器状态变更: idle
2025-08-11 09:51:15.591888: 所有认证方式监听已停止
2025-08-11 09:51:15.591888: 闸机认证服务已停止
