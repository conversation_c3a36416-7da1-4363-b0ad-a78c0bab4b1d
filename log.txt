2025-08-11 10:17:18.944257: currentXmlPath:D:\gdwork\code\a3g\packages\sea_socket\lib\src\protocols
2025-08-11 10:17:18.945254: 61
2025-08-11 10:17:19.348391: 开始初始化闸机协调器...
2025-08-11 10:17:19.348391: 可用串口: [COM1, COM4, COM3]
2025-08-11 10:17:19.354432: 连接闸机串口: COM1
2025-08-11 10:17:19.355405: 尝试连接串口: COM1, 波特率: 9600
2025-08-11 10:17:19.355405: 串口连接成功: COM1 at 9600 baud
2025-08-11 10:17:19.355405: 开始监听串口数据
2025-08-11 10:17:19.355405: 串口连接状态变化: true
2025-08-11 10:17:19.355405: 闸机串口连接成功
2025-08-11 10:17:19.356552: 串口 COM1 连接成功 (波特率: 9600)
2025-08-11 10:17:19.356552: 闸机串口服务初始化成功: COM1
2025-08-11 10:17:19.356552: 开始监听串口数据（通过 GateSerialManager 事件流）
2025-08-11 10:17:19.356552: 开始监听闸机串口命令
2025-08-11 10:17:19.357554: 闸机协调器初始化完成
2025-08-11 10:17:19.357554: 安全闸机系统初始化完成
2025-08-11 10:17:19.365560: 开始初始化MultiAuthManager...
2025-08-11 10:17:19.365560: 多认证管理器状态变更: initializing
2025-08-11 10:17:19.365560: 认证优先级管理器: 开始加载认证方式
2025-08-11 10:17:19.365560: 配置的排序: [人脸识别认证, 微信扫码认证, 读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 社保卡认证, 市民卡认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 微信二维码认证, 上海随申码认证, 汇文二维码, 支付宝二维码认证, 支付宝二维码认证（阿里信用）]
2025-08-11 10:17:19.366528: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-11 10:17:19.366528: 认证优先级管理器: 按配置顺序添加 微信扫码认证 -> 微信扫码
2025-08-11 10:17:19.366528: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-08-11 10:17:19.366528: 认证优先级管理器: 按配置顺序添加 社保卡认证 -> 社保卡
2025-08-11 10:17:19.366528: 认证优先级管理器: 按配置顺序添加 电子社保卡认证 -> 电子社保卡
2025-08-11 10:17:19.366528: 认证优先级管理器: 最终排序结果: 微信扫码 -> 读者证 -> 社保卡 -> 电子社保卡
2025-08-11 10:17:19.366528: 认证优先级管理器: 主要认证方式: 微信扫码
2025-08-11 10:17:19.366528: 多认证管理器: 从优先级管理器加载的认证方式: 微信扫码 -> 读者证 -> 社保卡 -> 电子社保卡
2025-08-11 10:17:19.366528: 多认证管理器: 当前默认显示方式: 微信扫码
2025-08-11 10:17:19.366528: 初始化二维码扫描认证服务
2025-08-11 10:17:19.367553: 初始化二维码扫码器
2025-08-11 10:17:19.367553: 二维码扫码器初始化完成
2025-08-11 10:17:19.367553: 二维码扫描认证服务初始化成功
2025-08-11 10:17:19.368539: 初始化共享二维码扫描认证服务
2025-08-11 10:17:19.368539: 微信扫码 认证服务初始化成功
2025-08-11 10:17:19.368539: 初始化读卡器认证服务
2025-08-11 10:17:19.368539: 读卡器认证服务初始化成功
2025-08-11 10:17:19.368539: 初始化共享读卡器认证服务
2025-08-11 10:17:19.368539: 读者证 认证服务初始化成功
2025-08-11 10:17:19.369534: 社保卡 认证服务初始化成功
2025-08-11 10:17:19.369534: 电子社保卡 认证服务初始化成功
2025-08-11 10:17:19.369534: 认证服务初始化完成，共初始化 4 种认证方式
2025-08-11 10:17:19.369534: 多认证管理器状态变更: idle
2025-08-11 10:17:19.369534: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-11 10:17:19.369534: MultiAuthManager初始化完成
2025-08-11 10:17:19.369534: 开始初始化SilencePageViewModel...
2025-08-11 10:17:19.369534: 闸机串口服务已经初始化
2025-08-11 10:17:19.369534: 开始初始化闸机认证服务...
2025-08-11 10:17:19.369534: 闸机认证服务初始化完成，启用认证方式: 人脸识别、读者证、AuthMethod.wechatScanQRCode
2025-08-11 10:17:19.370528: 开始初始化增强RFID服务...
2025-08-11 10:17:19.370528: 开始初始化增强RFID服务...
2025-08-11 10:17:19.370528: 书籍API服务初始化完成: http://your-api-server.com
2025-08-11 10:17:19.370528: SIP2图书信息服务初始化完成
2025-08-11 10:17:19.371519: 增强RFID服务初始化完成，配置了1个阅读器
2025-08-11 10:17:19.371519: 书籍API服务初始化完成: http://your-api-server.com
2025-08-11 10:17:19.371519: 增强RFID服务初始化完成
2025-08-11 10:17:19.371519: 书籍API服务初始化完成: http://api.library.com
2025-08-11 10:17:19.371519: 串口监听已经启动
2025-08-11 10:17:19.371519: SilencePageViewModel初始化完成
2025-08-11 10:17:19.372512: socket 连接成功,isBroadcast:false
2025-08-11 10:17:19.372512: changeSocketStatus:true
2025-08-11 10:17:19.372512: Sip2HeartBeatManager start loginACS:false askACS:false
2025-08-11 10:17:19.372512: Req msgType：Sip2MsgType.login ,length:72， ret:  9300CNhlsp_sip2|COsip2|CP3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AY1AZEC16
2025-08-11 10:17:19.459542: Rsp : 941AY1AZFDFC
2025-08-11 10:17:19.473882: loginRsp:{OK: 1, MsgSeqId: 1AZFDFC}
2025-08-11 10:17:19.473882: Sip2HeartBeatManager start loginACS:false askACS:true
2025-08-11 10:17:19.474789: 发送心跳
2025-08-11 10:17:19.474789: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY2AZFC9F
2025-08-11 10:17:19.497396: Rsp : 98YYYNNN00500320250811    1019032.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY2AZD529
2025-08-11 10:17:19.545940: dispose IndexPage
2025-08-11 10:17:19.545940: IndexPage dispose
2025-08-11 10:17:23.531778: 收到串口命令: enter_start (进馆开始)
2025-08-11 10:17:23.531778: 处理进馆开始
2025-08-11 10:17:23.531778: 闸机状态变更: GateState.enterStarted
2025-08-11 10:17:23.531778: 页面状态变更: SilencePageState.authenticating
2025-08-11 10:17:23.531778: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-11 10:17:23.531778: 启动闸机认证服务...
2025-08-11 10:17:23.531778: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-11 10:17:23.531778: 开始启动MultiAuthManager监听...
2025-08-11 10:17:23.531778: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:23.532775: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:23.532775: 多认证管理器状态变更: listening
2025-08-11 10:17:23.532775: 启动所有认证方式监听: [AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-11 10:17:23.532775: 准备启动 2 个物理认证服务
2025-08-11 10:17:23.532775: 开始二维码扫描认证监听
2025-08-11 10:17:23.532775: 开始二维码扫描监听
2025-08-11 10:17:23.532775: 二维码扫描认证监听启动成功
2025-08-11 10:17:23.533773: 微信扫码 认证服务启动成功
2025-08-11 10:17:23.533773: 开始读卡器认证监听
2025-08-11 10:17:23.533773: 强制重新配置读卡器以确保状态一致性
2025-08-11 10:17:23.533773: 完全重置读卡器连接和监听器状态...
2025-08-11 10:17:23.533773: 已移除读卡器状态监听器
2025-08-11 10:17:23.533773: 已移除标签数据监听器
2025-08-11 10:17:23.534771: 所有卡片监听器已移除
2025-08-11 10:17:23.534771: stopInventory newPort:null
2025-08-11 10:17:23.736539: 发送 关闭 阅读器newPort:null
2025-08-11 10:17:23.736539: 读卡器连接已完全关闭
2025-08-11 10:17:23.736539: 读卡器连接和监听器状态已完全重置
2025-08-11 10:17:23.737539: 添加设备配置: 读者证认证 -> 1个设备
2025-08-11 10:17:23.737539: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-11 10:17:23.738539: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-11 10:17:23.738539: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-11 10:17:23.738539: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-11 10:17:23.738539: 添加有效设备: type=10, id=10
2025-08-11 10:17:23.738539: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-11 10:17:23.738539: 添加有效设备: type=13, id=13
2025-08-11 10:17:23.738539: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-11 10:17:23.738539: 添加有效设备: type=12, id=12
2025-08-11 10:17:23.739544: 总共加载了3个设备配置
2025-08-11 10:17:23.739544: changeReaders
2025-08-11 10:17:23.739544: createIsolate isOpen:false,isOpening:false
2025-08-11 10:17:23.740531: createIsolate newport null
2025-08-11 10:17:24.243291: open():SendPort
2025-08-11 10:17:24.243291: untilDetcted():SendPort
2025-08-11 10:17:24.244292: 读卡器配置完成，共 3 个设备
2025-08-11 10:17:24.244292: 已移除读卡器状态监听器
2025-08-11 10:17:24.244292: 已移除标签数据监听器
2025-08-11 10:17:24.244292: 所有卡片监听器已移除
2025-08-11 10:17:24.244292: 已添加读卡器状态监听器
2025-08-11 10:17:24.244292: 已添加标签数据监听器
2025-08-11 10:17:24.244292: 开始监听卡片数据 - 所有监听器已就绪
2025-08-11 10:17:24.245288: 读卡器认证监听启动成功
2025-08-11 10:17:24.245288: 读者证、社保卡、电子社保卡 认证服务启动成功
2025-08-11 10:17:24.245288: 所有认证服务启动完成，成功启动 2 个服务
2025-08-11 10:17:24.245288: 当前可用的认证方式: 微信扫码、读者证、社保卡、电子社保卡
2025-08-11 10:17:24.245288: MultiAuthManager启动监听成功
2025-08-11 10:17:24.245288: 闸机认证服务启动成功（无UI模式），超时时间: 30秒
2025-08-11 10:17:24.245288: 闸机状态变更: GateState.enterScanning
2025-08-11 10:17:24.245288: 闸机认证服务启动成功
2025-08-11 10:17:24.245288: subThread :ReaderCommand.readerList
2025-08-11 10:17:24.246286: commandRsp:ReaderCommand.readerList
2025-08-11 10:17:24.246286: readerList：3,readerSetting：3
2025-08-11 10:17:24.246286: cacheUsedReaders:3
2025-08-11 10:17:24.246286: subThread :ReaderCommand.open
2025-08-11 10:17:24.246286: commandRsp:ReaderCommand.open
2025-08-11 10:17:24.412493: dc_init:0xb4 100
2025-08-11 10:17:24.412493: open reader readerType ：10 ret：0
2025-08-11 10:17:24.413493: open reader readerType ：13 ret：0
2025-08-11 10:17:24.421620: 去开启 读 监听
2025-08-11 10:17:24.421620: widget.port.isOpen:true
2025-08-11 10:17:24.423917: 打开COM4读写成功
2025-08-11 10:17:24.423917: 发送：[16, 54, 0d]
2025-08-11 10:17:24.423917: open reader readerType ：12 ret：0
2025-08-11 10:17:24.423917: [[10, 0], [13, 0], [12, 0]]
2025-08-11 10:17:24.424917: changeType:ReaderErrorType.openSuccess
2025-08-11 10:17:24.424917: 读卡器状态变化: ReaderErrorType.openSuccess
2025-08-11 10:17:24.424917: 读卡器连接成功
2025-08-11 10:17:24.424917: 读卡器连接成功，确保扫描状态正常
2025-08-11 10:17:24.424917: 恢复读卡器扫描状态...
2025-08-11 10:17:24.424917: 读卡器扫描状态已恢复
2025-08-11 10:17:24.425932: subThread :ReaderCommand.untilDetected
2025-08-11 10:17:24.426911: commandRsp:ReaderCommand.untilDetected
2025-08-11 10:17:24.426911: subThread :ReaderCommand.resumeInventory
2025-08-11 10:17:24.426911: commandRsp:ReaderCommand.resumeInventory
2025-08-11 10:17:24.932468: dc_config_card:0
2025-08-11 10:17:24.948446: dc_card_n_hex:1,len:0
2025-08-11 10:17:24.948446: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:25.014769: iReadCardBas ret:4294967294
2025-08-11 10:17:25.018758: 无卡
2025-08-11 10:17:25.018758: 无卡
2025-08-11 10:17:25.428711: dc_config_card:0
2025-08-11 10:17:25.444791: dc_card_n_hex:1,len:0
2025-08-11 10:17:25.444791: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:25.511666: iReadCardBas ret:4294967294
2025-08-11 10:17:25.511666: 无卡
2025-08-11 10:17:25.512681: 无卡
2025-08-11 10:17:25.932626: dc_config_card:0
2025-08-11 10:17:25.948558: dc_card_n_hex:1,len:0
2025-08-11 10:17:25.948558: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:26.016207: iReadCardBas ret:4294967294
2025-08-11 10:17:26.016207: 无卡
2025-08-11 10:17:26.016207: 无卡
2025-08-11 10:17:26.428636: dc_config_card:0
2025-08-11 10:17:26.444707: dc_card_n_hex:1,len:0
2025-08-11 10:17:26.444707: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:26.511527: iReadCardBas ret:4294967294
2025-08-11 10:17:26.511527: 无卡
2025-08-11 10:17:26.512528: 无卡
2025-08-11 10:17:26.932712: dc_config_card:0
2025-08-11 10:17:27.020755: dc_card_n_hex:0,len:4
2025-08-11 10:17:27.020755: parseHD100Info_2:[70, 67, 57, 56, 66, 68, 69, 50]
2025-08-11 10:17:27.027767: dc_authentication_pass:0
2025-08-11 10:17:27.036786: dc_read:0
2025-08-11 10:17:27.036786: data:67643131313131310000000000000000,coder:Std14443ACoder
2025-08-11 10:17:27.036786: parseRet：{"barCode":"gd111111"},decoder:14443AStd
2025-08-11 10:17:27.037788: 🎯 HWTagProvider.changeAddedItem: 收到 1 个新标签
2025-08-11 10:17:27.037788: 🏷️ 新标签[0]: uid=FC98BDE2, barCode=gd111111, readerType=10
2025-08-11 10:17:27.037788: 🎯 HWTagProvider: 当前总标签数 1, 新增标签数 1
2025-08-11 10:17:27.037788: 检测到二维码数据: gd111111
2025-08-11 10:17:27.038784: 未知的二维码类型: gd111111
2025-08-11 10:17:27.038784: 检测到读卡器数据: 1条
2025-08-11 10:17:27.038784: 读卡器数据认证：
2025-08-11 10:17:27.038784:   设备类型: 10
2025-08-11 10:17:27.038784:   条码: gd111111
2025-08-11 10:17:27.038784:   标签UID: FC98BDE2
2025-08-11 10:17:27.039781:   对应登录类型: AuthLoginType.readerCard
2025-08-11 10:17:27.039781:   根据读卡器类型10确定认证方式为: 读者证
2025-08-11 10:17:27.039781:   开始调用认证API: gd111111
2025-08-11 10:17:27.039781: 正在认证用户: gd111111, 方式: 读者证
2025-08-11 10:17:27.039781: 多认证管理器: 切换显示方式 微信扫码 -> 读者证
2025-08-11 10:17:27.039781: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:27.039781: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:27.040796: 多认证管理器: 读者证获得认证请求锁
2025-08-11 10:17:27.040796: 63 CardType 值为空
2025-08-11 10:17:27.040796: Req msgType：Sip2MsgType.patronInformation ,length:73， ret:  6300120250811    101727  Y       AOhlsp|AAgd111111|TY|BP1|BQ20|AY3AZEF54
2025-08-11 10:17:27.040796: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:27.103750: iReadCardBas ret:4294967294
2025-08-11 10:17:27.103750: 无卡
2025-08-11 10:17:27.103750: 无卡
2025-08-11 10:17:27.103750: 检测到了卡 来暂停 ：1
2025-08-11 10:17:27.222577: Rsp : 64YYYYYYYYYYYYYY00020250811    101911000000000000000000000000AOhlsp|AAgd111111|XO|AEgd111111|BLN|CQN|JF|BE|AF读者未找到,请联系管理员寻求帮助|AG|AY3AZBFDF
2025-08-11 10:17:27.234738: rspInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250811    101911, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: gd111111, PersonName: gd111111, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 3AZBFDF}
2025-08-11 10:17:27.235606: patronInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250811    101911, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: gd111111, PersonName: gd111111, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 3AZBFDF}
2025-08-11 10:17:27.235606: SIP2认证失败: 读者未找到,请联系管理员寻求帮助
2025-08-11 10:17:27.235606: 认证失败，继续监听新的卡片扫描...
2025-08-11 10:17:27.235606:   认证结果: AuthStatus.failureNoMatch, 方式: 读者证
2025-08-11 10:17:27.235606: 认证失败，继续监听下一个用户
2025-08-11 10:17:27.236454: 恢复读卡器扫描状态...
2025-08-11 10:17:27.236454: 读卡器扫描状态已恢复
2025-08-11 10:17:27.236454: 多认证管理器: 收到认证结果: 读者证 - failureNoMatch
2025-08-11 10:17:27.236454: 认证状态变化: MultiAuthState.authenticating
2025-08-11 10:17:27.236454: 认证状态变化: MultiAuthState.authenticating
2025-08-11 10:17:27.236454: 多认证管理器状态变更: authenticating
2025-08-11 10:17:27.236454: 多认证管理器: 认证失败(无匹配): 读者证，显示失败信息
2025-08-11 10:17:27.236454: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-11 10:17:27.237476: 多认证管理器: 收到认证结果: 读者证 - failureNoMatch
2025-08-11 10:17:27.237476: 多认证管理器: 认证失败(无匹配): 读者证，显示失败信息
2025-08-11 10:17:27.237476: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-11 10:17:27.237476: 多认证管理器: 收到认证结果: 读者证 - failureNoMatch
2025-08-11 10:17:27.237476: 多认证管理器: 认证失败(无匹配): 读者证，显示失败信息
2025-08-11 10:17:27.237476: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-11 10:17:27.237476: 非认证状态收到认证结果，忽略
2025-08-11 10:17:27.237476: 收到认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-11 10:17:27.238485: 收到闸机认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-11 10:17:27.238485: 页面状态变更: SilencePageState.authFailed
2025-08-11 10:17:27.238485: 非认证状态收到认证结果，忽略
2025-08-11 10:17:27.238485: 收到认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-11 10:17:27.238485: 收到闸机认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-11 10:17:27.238485: 页面状态变更: SilencePageState.authFailed
2025-08-11 10:17:27.238485: 非认证状态收到认证结果，忽略
2025-08-11 10:17:27.239461: 收到认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-11 10:17:27.239461: 收到闸机认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-11 10:17:27.239461: 页面状态变更: SilencePageState.authFailed
2025-08-11 10:17:27.239461: subThread :ReaderCommand.resumeInventory
2025-08-11 10:17:27.239461: commandRsp:ReaderCommand.resumeInventory
2025-08-11 10:17:27.428675: dc_config_card:0
2025-08-11 10:17:27.524709: dc_card_n_hex:0,len:4
2025-08-11 10:17:27.524709: parseHD100Info_2:[70, 67, 57, 56, 66, 68, 69, 50]
2025-08-11 10:17:27.531785: dc_authentication_pass:0
2025-08-11 10:17:27.540690: dc_read:0
2025-08-11 10:17:27.540690: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:27.608028: iReadCardBas ret:4294967294
2025-08-11 10:17:27.608028: 无卡
2025-08-11 10:17:27.608028: 无卡
2025-08-11 10:17:27.608028: 检测到了卡 来暂停 ：1
2025-08-11 10:17:27.609035: 检测到二维码数据: gd111111
2025-08-11 10:17:27.609035: 未知的二维码类型: gd111111
2025-08-11 10:17:30.237300: 多认证管理器: 失败信息显示完成，恢复到监听状态
2025-08-11 10:17:30.237300: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:30.237300: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:30.237300: 多认证管理器状态变更: listening
2025-08-11 10:17:32.236917: 多认证管理器: 认证请求锁已释放（之前为读者证）
2025-08-11 10:17:32.236917: 多认证管理器: 还原到默认显示方式: 微信扫码
2025-08-11 10:17:32.236917: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:32.237933: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:32.240291: 停止闸机认证服务...
2025-08-11 10:17:32.241292: 闸机状态变更: GateState.idle
2025-08-11 10:17:32.241292: 页面状态变更: SilencePageState.welcome
2025-08-11 10:17:32.242288: 停止所有认证方式监听
2025-08-11 10:17:32.242288: 停止微信扫码认证监听
2025-08-11 10:17:32.242288: 停止二维码扫描认证监听
2025-08-11 10:17:32.242288: 停止读者证认证监听
2025-08-11 10:17:32.242288: 停止读卡器认证监听
2025-08-11 10:17:32.242288: 停止社保卡认证监听
2025-08-11 10:17:32.243286: 停止读卡器认证监听
2025-08-11 10:17:32.243286: 停止电子社保卡认证监听
2025-08-11 10:17:32.243286: 停止读卡器认证监听
2025-08-11 10:17:32.243286: 二维码扫描认证监听已停止
2025-08-11 10:17:32.244289: 微信扫码认证服务监听已停止
2025-08-11 10:17:32.244289: 已移除读卡器状态监听器
2025-08-11 10:17:32.244289: 已移除标签数据监听器
2025-08-11 10:17:32.244289: 所有卡片监听器已移除
2025-08-11 10:17:32.244289: 暂停读卡器扫描（保持连接）...
2025-08-11 10:17:32.244289: stopInventory newPort:SendPort
2025-08-11 10:17:32.244289: 已移除读卡器状态监听器
2025-08-11 10:17:32.244289: 已移除标签数据监听器
2025-08-11 10:17:32.244289: 所有卡片监听器已移除
2025-08-11 10:17:32.244289: 暂停读卡器扫描（保持连接）...
2025-08-11 10:17:32.245288: stopInventory newPort:SendPort
2025-08-11 10:17:32.245288: 已移除读卡器状态监听器
2025-08-11 10:17:32.245288: 已移除标签数据监听器
2025-08-11 10:17:32.245288: 所有卡片监听器已移除
2025-08-11 10:17:32.245288: 暂停读卡器扫描（保持连接）...
2025-08-11 10:17:32.245288: stopInventory newPort:SendPort
2025-08-11 10:17:32.245288: subThread :ReaderCommand.stopInventory
2025-08-11 10:17:32.245288: commandRsp:ReaderCommand.stopInventory
2025-08-11 10:17:32.246286: subThread :ReaderCommand.stopInventory
2025-08-11 10:17:32.246286: commandRsp:ReaderCommand.stopInventory
2025-08-11 10:17:32.246286: subThread :ReaderCommand.stopInventory
2025-08-11 10:17:32.246286: commandRsp:ReaderCommand.stopInventory
2025-08-11 10:17:32.345697: 读卡器扫描已暂停，USB/串口连接保持
2025-08-11 10:17:32.345697: 读卡器认证监听已停止（连接保持）
2025-08-11 10:17:32.346691: 读者证认证服务监听已停止
2025-08-11 10:17:32.346691: 读卡器扫描已暂停，USB/串口连接保持
2025-08-11 10:17:32.346691: 读卡器认证监听已停止（连接保持）
2025-08-11 10:17:32.346691: 社保卡认证服务监听已停止
2025-08-11 10:17:32.347690: 读卡器扫描已暂停，USB/串口连接保持
2025-08-11 10:17:32.347690: 读卡器认证监听已停止（连接保持）
2025-08-11 10:17:32.347690: 电子社保卡认证服务监听已停止
2025-08-11 10:17:32.347690: 多认证管理器状态变更: idle
2025-08-11 10:17:32.347690: 所有认证方式监听已停止
2025-08-11 10:17:32.347690: 闸机认证服务已停止
2025-08-11 10:17:40.210869: 收到串口命令: enter_start (进馆开始)
2025-08-11 10:17:40.210869: 处理进馆开始
2025-08-11 10:17:40.211857: 闸机状态变更: GateState.enterStarted
2025-08-11 10:17:40.211857: 页面状态变更: SilencePageState.authenticating
2025-08-11 10:17:40.211857: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-11 10:17:40.211857: 启动闸机认证服务...
2025-08-11 10:17:40.211857: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-11 10:17:40.211857: 开始启动MultiAuthManager监听...
2025-08-11 10:17:40.211857: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:40.211857: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:40.211857: 多认证管理器状态变更: listening
2025-08-11 10:17:40.211857: 启动所有认证方式监听: [AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-11 10:17:40.211857: 准备启动 2 个物理认证服务
2025-08-11 10:17:40.212853: 开始二维码扫描认证监听
2025-08-11 10:17:40.212853: 开始二维码扫描监听
2025-08-11 10:17:40.212853: 二维码扫描认证监听启动成功
2025-08-11 10:17:40.212853: 微信扫码 认证服务启动成功
2025-08-11 10:17:40.212853: 开始读卡器认证监听
2025-08-11 10:17:40.212853: 强制重新配置读卡器以确保状态一致性
2025-08-11 10:17:40.212853: 完全重置读卡器连接和监听器状态...
2025-08-11 10:17:40.213851: 已移除读卡器状态监听器
2025-08-11 10:17:40.213851: 已移除标签数据监听器
2025-08-11 10:17:40.213851: 所有卡片监听器已移除
2025-08-11 10:17:40.213851: stopInventory newPort:SendPort
2025-08-11 10:17:40.214848: subThread :ReaderCommand.stopInventory
2025-08-11 10:17:40.214848: commandRsp:ReaderCommand.stopInventory
2025-08-11 10:17:40.416118: 发送 关闭 阅读器newPort:SendPort
2025-08-11 10:17:40.416118: subThread :ReaderCommand.close
2025-08-11 10:17:40.416118: commandRsp:ReaderCommand.close
2025-08-11 10:17:40.416118: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 电子社保解码器, patronUrl: null, dataType: null, accessKey: null, appArea: null, appKey: null, appSecret: null, basicCode: null, bussiType: null, libCode: null, machineUUID: null, method: null, secretKey: null, tradeCode: null, port: COM4, openCmd: 16 54 0d, closeCmd: 16 55 0d}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-08-11 10:17:40.416118: close:T10Bridge
2025-08-11 10:17:40.417115: dc_exit:0
2025-08-11 10:17:40.417115: close:HD100SSBridge
2025-08-11 10:17:40.417115: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-11 10:17:40.417115: HD100SS close result: -1
2025-08-11 10:17:40.417115: close:ScanerBridge
2025-08-11 10:17:40.418113: 发送：[16, 55, 0d]
2025-08-11 10:17:40.440322: close:done
2025-08-11 10:17:40.440322: changeType:ReaderErrorType.closeSuccess
2025-08-11 10:17:40.440322: already close all reader
2025-08-11 10:17:40.517628: 读卡器连接已完全关闭
2025-08-11 10:17:40.518628: 读卡器连接和监听器状态已完全重置
2025-08-11 10:17:40.518628: 添加设备配置: 读者证认证 -> 1个设备
2025-08-11 10:17:40.518628: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-11 10:17:40.518628: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-11 10:17:40.518628: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-11 10:17:40.518628: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-11 10:17:40.518628: 添加有效设备: type=10, id=10
2025-08-11 10:17:40.518628: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-11 10:17:40.518628: 添加有效设备: type=13, id=13
2025-08-11 10:17:40.519644: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-11 10:17:40.519644: 添加有效设备: type=12, id=12
2025-08-11 10:17:40.519644: 总共加载了3个设备配置
2025-08-11 10:17:40.519644: stopInventory newPort:SendPort
2025-08-11 10:17:40.519644: subThread :ReaderCommand.stopInventory
2025-08-11 10:17:40.519644: commandRsp:ReaderCommand.stopInventory
2025-08-11 10:17:40.620916: 发送 关闭 阅读器newPort:SendPort
2025-08-11 10:17:40.620916: 读卡器连接已完全关闭
2025-08-11 10:17:40.620916: changeReaders
2025-08-11 10:17:40.621952: createIsolate isOpen:true,isOpening:false
2025-08-11 10:17:40.621952: open():SendPort
2025-08-11 10:17:40.621952: untilDetcted():SendPort
2025-08-11 10:17:40.622928: 读卡器配置完成，共 3 个设备
2025-08-11 10:17:40.622928: 已移除读卡器状态监听器
2025-08-11 10:17:40.622928: 已移除标签数据监听器
2025-08-11 10:17:40.622928: 所有卡片监听器已移除
2025-08-11 10:17:40.622928: 已添加读卡器状态监听器
2025-08-11 10:17:40.622928: 已添加标签数据监听器
2025-08-11 10:17:40.622928: 开始监听卡片数据 - 所有监听器已就绪
2025-08-11 10:17:40.622928: 读卡器认证监听启动成功
2025-08-11 10:17:40.623925: 读者证、社保卡、电子社保卡 认证服务启动成功
2025-08-11 10:17:40.623925: 所有认证服务启动完成，成功启动 2 个服务
2025-08-11 10:17:40.623925: 当前可用的认证方式: 微信扫码、读者证、社保卡、电子社保卡
2025-08-11 10:17:40.623925: MultiAuthManager启动监听成功
2025-08-11 10:17:40.623925: 闸机认证服务启动成功（无UI模式），超时时间: 30秒
2025-08-11 10:17:40.623925: 闸机状态变更: GateState.enterScanning
2025-08-11 10:17:40.623925: 闸机认证服务启动成功
2025-08-11 10:17:40.623925: subThread :ReaderCommand.close
2025-08-11 10:17:40.623925: commandRsp:ReaderCommand.close
2025-08-11 10:17:40.624923: cacheUsedReaders:()
2025-08-11 10:17:40.624923: close:done
2025-08-11 10:17:40.624923: changeType:ReaderErrorType.closeSuccess
2025-08-11 10:17:40.624923: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-08-11 10:17:40.624923: already close all reader
2025-08-11 10:17:40.625920: subThread :ReaderCommand.readerList
2025-08-11 10:17:40.625920: commandRsp:ReaderCommand.readerList
2025-08-11 10:17:40.625920: readerList：3,readerSetting：3
2025-08-11 10:17:40.625920: cacheUsedReaders:3
2025-08-11 10:17:40.626918: subThread :ReaderCommand.open
2025-08-11 10:17:40.626918: commandRsp:ReaderCommand.open
2025-08-11 10:17:40.724621: dc_init:0xb4 100
2025-08-11 10:17:40.724621: open reader readerType ：10 ret：0
2025-08-11 10:17:40.724621: open reader readerType ：13 ret：0
2025-08-11 10:17:40.736785: 去开启 读 监听
2025-08-11 10:17:40.736785: widget.port.isOpen:true
2025-08-11 10:17:40.738814: 打开COM4读写成功
2025-08-11 10:17:40.738814: 发送：[16, 54, 0d]
2025-08-11 10:17:40.738814: open reader readerType ：12 ret：0
2025-08-11 10:17:40.739836: [[10, 0], [13, 0], [12, 0]]
2025-08-11 10:17:40.739836: changeType:ReaderErrorType.openSuccess
2025-08-11 10:17:40.739836: 读卡器状态变化: ReaderErrorType.openSuccess
2025-08-11 10:17:40.740811: 读卡器连接成功
2025-08-11 10:17:40.740811: 读卡器连接成功，确保扫描状态正常
2025-08-11 10:17:40.740811: 恢复读卡器扫描状态...
2025-08-11 10:17:40.740811: 读卡器扫描状态已恢复
2025-08-11 10:17:40.741844: subThread :ReaderCommand.untilDetected
2025-08-11 10:17:40.741844: commandRsp:ReaderCommand.untilDetected
2025-08-11 10:17:40.742512: subThread :ReaderCommand.resumeInventory
2025-08-11 10:17:40.742512: commandRsp:ReaderCommand.resumeInventory
2025-08-11 10:17:41.244782: dc_config_card:0
2025-08-11 10:17:41.260794: dc_card_n_hex:1,len:0
2025-08-11 10:17:41.260794: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:41.328065: iReadCardBas ret:4294967294
2025-08-11 10:17:41.328065: 无卡
2025-08-11 10:17:41.328065: 无卡
2025-08-11 10:17:41.748042: dc_config_card:0
2025-08-11 10:17:41.764053: dc_card_n_hex:1,len:0
2025-08-11 10:17:41.764053: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:41.830755: iReadCardBas ret:4294967294
2025-08-11 10:17:41.830755: 无卡
2025-08-11 10:17:41.831756: 无卡
2025-08-11 10:17:42.244737: dc_config_card:0
2025-08-11 10:17:42.332724: dc_card_n_hex:0,len:4
2025-08-11 10:17:42.332724: parseHD100Info_2:[70, 67, 57, 56, 66, 68, 69, 50]
2025-08-11 10:17:42.340722: dc_authentication_pass:0
2025-08-11 10:17:42.347850: dc_read:0
2025-08-11 10:17:42.348853: data:67643131313131310000000000000000,coder:Std14443ACoder
2025-08-11 10:17:42.348853: parseRet：{"barCode":"gd111111"},decoder:14443AStd
2025-08-11 10:17:42.348853: 🎯 HWTagProvider.changeAddedItem: 收到 1 个新标签
2025-08-11 10:17:42.348853: 🏷️ 新标签[0]: uid=FC98BDE2, barCode=gd111111, readerType=10
2025-08-11 10:17:42.348853: 🎯 HWTagProvider: 当前总标签数 1, 新增标签数 1
2025-08-11 10:17:42.349858: 检测到二维码数据: gd111111
2025-08-11 10:17:42.349858: 未知的二维码类型: gd111111
2025-08-11 10:17:42.349858: 检测到二维码数据: gd111111
2025-08-11 10:17:42.349858: 未知的二维码类型: gd111111
2025-08-11 10:17:42.350846: 检测到读卡器数据: 1条
2025-08-11 10:17:42.350846: 读卡器数据认证：
2025-08-11 10:17:42.350846:   设备类型: 10
2025-08-11 10:17:42.350846:   条码: gd111111
2025-08-11 10:17:42.350846:   标签UID: FC98BDE2
2025-08-11 10:17:42.350846:   对应登录类型: AuthLoginType.readerCard
2025-08-11 10:17:42.350846:   根据读卡器类型10确定认证方式为: 读者证
2025-08-11 10:17:42.350846:   开始调用认证API: gd111111
2025-08-11 10:17:42.350846: 正在认证用户: gd111111, 方式: 读者证
2025-08-11 10:17:42.350846: 多认证管理器: 切换显示方式 微信扫码 -> 读者证
2025-08-11 10:17:42.350846: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:42.351843: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:42.351843: 多认证管理器: 读者证获得认证请求锁
2025-08-11 10:17:42.351843: 63 CardType 值为空
2025-08-11 10:17:42.351843: Req msgType：Sip2MsgType.patronInformation ,length:73， ret:  6300120250811    101742  Y       AOhlsp|AAgd111111|TY|BP1|BQ20|AY4AZEF56
2025-08-11 10:17:42.351843: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:42.414509: iReadCardBas ret:4294967294
2025-08-11 10:17:42.414509: 无卡
2025-08-11 10:17:42.415511: 无卡
2025-08-11 10:17:42.415511: 检测到了卡 来暂停 ：1
2025-08-11 10:17:42.458375: Rsp : 64YYYYYYYYYYYYYY00020250811    101926000000000000000000000000AOhlsp|AAgd111111|XO|AEgd111111|BLN|CQN|JF|BE|AF读者未找到,请联系管理员寻求帮助|AG|AY4AZBFD8
2025-08-11 10:17:42.482965: rspInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250811    101926, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: gd111111, PersonName: gd111111, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 4AZBFD8}
2025-08-11 10:17:42.482965: patronInfo:{PatronStatus: YYYYYYYYYYYYYY, Language: 000, TransactionDate: 20250811    101926, HoldItemsCount: 0000, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount: 0000, RecallItemsCount: 0000, UnavailableItemsCount: 0000, InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: gd111111, PersonName: gd111111, HoldItemsLimit: , OverdueItemsLimit: , ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: N, ValidPatronPassword: N, FeeAmount: , ChargeAmount: , PatronTotalFine: , Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: , OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: 读者未找到,请联系管理员寻求帮助, PrintLine: , MsgSeqId: 4AZBFD8}
2025-08-11 10:17:42.483781: SIP2认证失败: 读者未找到,请联系管理员寻求帮助
2025-08-11 10:17:42.483781: 认证失败，继续监听新的卡片扫描...
2025-08-11 10:17:42.483781:   认证结果: AuthStatus.failureNoMatch, 方式: 读者证
2025-08-11 10:17:42.483781: 认证失败，继续监听下一个用户
2025-08-11 10:17:42.485216: 恢复读卡器扫描状态...
2025-08-11 10:17:42.485216: 读卡器扫描状态已恢复
2025-08-11 10:17:42.485216: 多认证管理器: 收到认证结果: 读者证 - failureNoMatch
2025-08-11 10:17:42.485216: 认证状态变化: MultiAuthState.authenticating
2025-08-11 10:17:42.485216: 认证状态变化: MultiAuthState.authenticating
2025-08-11 10:17:42.485216: 多认证管理器状态变更: authenticating
2025-08-11 10:17:42.485216: 多认证管理器: 认证失败(无匹配): 读者证，显示失败信息
2025-08-11 10:17:42.485216: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-11 10:17:42.486346: 多认证管理器: 收到认证结果: 读者证 - failureNoMatch
2025-08-11 10:17:42.486346: 多认证管理器: 认证失败(无匹配): 读者证，显示失败信息
2025-08-11 10:17:42.486346: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-11 10:17:42.486346: 多认证管理器: 收到认证结果: 读者证 - failureNoMatch
2025-08-11 10:17:42.486346: 多认证管理器: 认证失败(无匹配): 读者证，显示失败信息
2025-08-11 10:17:42.486346: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-11 10:17:42.486346: 非认证状态收到认证结果，忽略
2025-08-11 10:17:42.486346: 收到认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-11 10:17:42.486346: 收到闸机认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-11 10:17:42.486346: 页面状态变更: SilencePageState.authFailed
2025-08-11 10:17:42.487377: 非认证状态收到认证结果，忽略
2025-08-11 10:17:42.487377: 收到认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-11 10:17:42.487377: 收到闸机认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-11 10:17:42.487377: 页面状态变更: SilencePageState.authFailed
2025-08-11 10:17:42.487377: 非认证状态收到认证结果，忽略
2025-08-11 10:17:42.488346: 收到认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-11 10:17:42.488346: 收到闸机认证结果: AuthStatus.failureNoMatch, 用户: null
2025-08-11 10:17:42.488346: 页面状态变更: SilencePageState.authFailed
2025-08-11 10:17:42.488346: subThread :ReaderCommand.resumeInventory
2025-08-11 10:17:42.488346: commandRsp:ReaderCommand.resumeInventory
2025-08-11 10:17:42.748615: dc_config_card:0
2025-08-11 10:17:42.844106: dc_card_n_hex:0,len:4
2025-08-11 10:17:42.844106: parseHD100Info_2:[70, 67, 57, 56, 66, 68, 69, 50]
2025-08-11 10:17:42.852606: dc_authentication_pass:0
2025-08-11 10:17:42.860721: dc_read:0
2025-08-11 10:17:42.861081: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:42.927624: iReadCardBas ret:4294967294
2025-08-11 10:17:42.927624: 无卡
2025-08-11 10:17:42.927624: 无卡
2025-08-11 10:17:42.928626: 检测到了卡 来暂停 ：1
2025-08-11 10:17:42.928626: 检测到二维码数据: gd111111
2025-08-11 10:17:42.928626: 未知的二维码类型: gd111111
2025-08-11 10:17:42.928626: 检测到二维码数据: gd111111
2025-08-11 10:17:42.928626: 未知的二维码类型: gd111111
2025-08-11 10:17:45.484968: 多认证管理器: 失败信息显示完成，恢复到监听状态
2025-08-11 10:17:45.485982: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:45.485982: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:45.485982: 多认证管理器状态变更: listening
2025-08-11 10:17:47.485905: 多认证管理器: 认证请求锁已释放（之前为读者证）
2025-08-11 10:17:47.485905: 多认证管理器: 还原到默认显示方式: 微信扫码
2025-08-11 10:17:47.486906: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:47.486906: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:47.490003: 停止闸机认证服务...
2025-08-11 10:17:47.490003: 闸机状态变更: GateState.idle
2025-08-11 10:17:47.490858: 页面状态变更: SilencePageState.welcome
2025-08-11 10:17:47.490858: 停止所有认证方式监听
2025-08-11 10:17:47.490858: 停止微信扫码认证监听
2025-08-11 10:17:47.490858: 停止二维码扫描认证监听
2025-08-11 10:17:47.490858: 停止读者证认证监听
2025-08-11 10:17:47.490858: 停止读卡器认证监听
2025-08-11 10:17:47.491859: 停止社保卡认证监听
2025-08-11 10:17:47.491859: 停止读卡器认证监听
2025-08-11 10:17:47.491859: 停止电子社保卡认证监听
2025-08-11 10:17:47.491859: 停止读卡器认证监听
2025-08-11 10:17:47.492855: 二维码扫描认证监听已停止
2025-08-11 10:17:47.492855: 微信扫码认证服务监听已停止
2025-08-11 10:17:47.492855: 已移除读卡器状态监听器
2025-08-11 10:17:47.492855: 已移除标签数据监听器
2025-08-11 10:17:47.492855: 所有卡片监听器已移除
2025-08-11 10:17:47.492855: 暂停读卡器扫描（保持连接）...
2025-08-11 10:17:47.493852: stopInventory newPort:SendPort
2025-08-11 10:17:47.493852: 已移除读卡器状态监听器
2025-08-11 10:17:47.493852: 已移除标签数据监听器
2025-08-11 10:17:47.493852: 所有卡片监听器已移除
2025-08-11 10:17:47.493852: 暂停读卡器扫描（保持连接）...
2025-08-11 10:17:47.493852: stopInventory newPort:SendPort
2025-08-11 10:17:47.493852: 已移除读卡器状态监听器
2025-08-11 10:17:47.493852: 已移除标签数据监听器
2025-08-11 10:17:47.493852: 所有卡片监听器已移除
2025-08-11 10:17:47.493852: 暂停读卡器扫描（保持连接）...
2025-08-11 10:17:47.493852: stopInventory newPort:SendPort
2025-08-11 10:17:47.494849: subThread :ReaderCommand.stopInventory
2025-08-11 10:17:47.494849: commandRsp:ReaderCommand.stopInventory
2025-08-11 10:17:47.494849: subThread :ReaderCommand.stopInventory
2025-08-11 10:17:47.494849: commandRsp:ReaderCommand.stopInventory
2025-08-11 10:17:47.494849: subThread :ReaderCommand.stopInventory
2025-08-11 10:17:47.494849: commandRsp:ReaderCommand.stopInventory
2025-08-11 10:17:47.596015: 读卡器扫描已暂停，USB/串口连接保持
2025-08-11 10:17:47.596816: 读卡器认证监听已停止（连接保持）
2025-08-11 10:17:47.596816: 读者证认证服务监听已停止
2025-08-11 10:17:47.596816: 读卡器扫描已暂停，USB/串口连接保持
2025-08-11 10:17:47.596816: 读卡器认证监听已停止（连接保持）
2025-08-11 10:17:47.596816: 社保卡认证服务监听已停止
2025-08-11 10:17:47.597813: 读卡器扫描已暂停，USB/串口连接保持
2025-08-11 10:17:47.597813: 读卡器认证监听已停止（连接保持）
2025-08-11 10:17:47.597813: 电子社保卡认证服务监听已停止
2025-08-11 10:17:47.597813: 多认证管理器状态变更: idle
2025-08-11 10:17:47.597813: 所有认证方式监听已停止
2025-08-11 10:17:47.597813: 闸机认证服务已停止
2025-08-11 10:17:51.515671: 收到串口命令: enter_start (进馆开始)
2025-08-11 10:17:51.515671: 处理进馆开始
2025-08-11 10:17:51.515671: 闸机状态变更: GateState.enterStarted
2025-08-11 10:17:51.515671: 页面状态变更: SilencePageState.authenticating
2025-08-11 10:17:51.515671: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-11 10:17:51.515671: 启动闸机认证服务...
2025-08-11 10:17:51.515671: MultiAuthManager当前状态: MultiAuthState.idle
2025-08-11 10:17:51.515671: 开始启动MultiAuthManager监听...
2025-08-11 10:17:51.516668: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:51.516668: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:51.516668: 多认证管理器状态变更: listening
2025-08-11 10:17:51.516668: 启动所有认证方式监听: [AuthMethod.wechatScanQRCode, AuthMethod.readerCard, AuthMethod.socialSecurityCard, AuthMethod.eletricSocialSecurityCard]
2025-08-11 10:17:51.516668: 准备启动 2 个物理认证服务
2025-08-11 10:17:51.516668: 开始二维码扫描认证监听
2025-08-11 10:17:51.516668: 开始二维码扫描监听
2025-08-11 10:17:51.516668: 二维码扫描认证监听启动成功
2025-08-11 10:17:51.517665: 微信扫码 认证服务启动成功
2025-08-11 10:17:51.517665: 开始读卡器认证监听
2025-08-11 10:17:51.517665: 强制重新配置读卡器以确保状态一致性
2025-08-11 10:17:51.517665: 完全重置读卡器连接和监听器状态...
2025-08-11 10:17:51.517665: 已移除读卡器状态监听器
2025-08-11 10:17:51.517665: 已移除标签数据监听器
2025-08-11 10:17:51.517665: 所有卡片监听器已移除
2025-08-11 10:17:51.518663: stopInventory newPort:SendPort
2025-08-11 10:17:51.518663: subThread :ReaderCommand.stopInventory
2025-08-11 10:17:51.518663: commandRsp:ReaderCommand.stopInventory
2025-08-11 10:17:51.720442: 发送 关闭 阅读器newPort:SendPort
2025-08-11 10:17:51.720442: subThread :ReaderCommand.close
2025-08-11 10:17:51.720442: commandRsp:ReaderCommand.close
2025-08-11 10:17:51.720442: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 电子社保解码器, patronUrl: null, dataType: null, accessKey: null, appArea: null, appKey: null, appSecret: null, basicCode: null, bussiType: null, libCode: null, machineUUID: null, method: null, secretKey: null, tradeCode: null, port: COM4, openCmd: 16 54 0d, closeCmd: 16 55 0d}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 12, type: 3, readerType: 12, selectedCardType: 平台电子社保码, extras: null}})
2025-08-11 10:17:51.721365: close:T10Bridge
2025-08-11 10:17:51.721365: dc_exit:0
2025-08-11 10:17:51.721365: close:HD100SSBridge
2025-08-11 10:17:51.721365: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-08-11 10:17:51.722095: HD100SS close result: -1
2025-08-11 10:17:51.722095: close:ScanerBridge
2025-08-11 10:17:51.722095: 发送：[16, 55, 0d]
2025-08-11 10:17:51.743226: close:done
2025-08-11 10:17:51.743226: changeType:ReaderErrorType.closeSuccess
2025-08-11 10:17:51.743226: already close all reader
2025-08-11 10:17:51.821754: 读卡器连接已完全关闭
2025-08-11 10:17:51.822349: 读卡器连接和监听器状态已完全重置
2025-08-11 10:17:51.822349: 添加设备配置: 读者证认证 -> 1个设备
2025-08-11 10:17:51.822349: 添加设备配置: 社保卡认证 -> 1个设备
2025-08-11 10:17:51.822349: 添加设备配置: 电子社保卡认证 -> 1个设备
2025-08-11 10:17:51.822349: 添加设备配置: 微信扫码认证 -> 1个设备
2025-08-11 10:17:51.822349: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-08-11 10:17:51.822349: 添加有效设备: type=10, id=10
2025-08-11 10:17:51.822349: 验证读卡器配置: 类型=13, 解码器=不解析
2025-08-11 10:17:51.822349: 添加有效设备: type=13, id=13
2025-08-11 10:17:51.822349: 验证读卡器配置: 类型=12, 解码器=电子社保解码器
2025-08-11 10:17:51.823381: 添加有效设备: type=12, id=12
2025-08-11 10:17:51.823381: 总共加载了3个设备配置
2025-08-11 10:17:51.823381: stopInventory newPort:SendPort
2025-08-11 10:17:51.823381: subThread :ReaderCommand.stopInventory
2025-08-11 10:17:51.823381: commandRsp:ReaderCommand.stopInventory
2025-08-11 10:17:51.924173: 发送 关闭 阅读器newPort:SendPort
2025-08-11 10:17:51.924173: 读卡器连接已完全关闭
2025-08-11 10:17:51.924173: changeReaders
2025-08-11 10:17:51.925175: createIsolate isOpen:true,isOpening:false
2025-08-11 10:17:51.925175: open():SendPort
2025-08-11 10:17:51.925175: untilDetcted():SendPort
2025-08-11 10:17:51.925175: 读卡器配置完成，共 3 个设备
2025-08-11 10:17:51.925175: 已移除读卡器状态监听器
2025-08-11 10:17:51.926169: 已移除标签数据监听器
2025-08-11 10:17:51.926444: 所有卡片监听器已移除
2025-08-11 10:17:51.926444: 已添加读卡器状态监听器
2025-08-11 10:17:51.926444: 已添加标签数据监听器
2025-08-11 10:17:51.926444: 开始监听卡片数据 - 所有监听器已就绪
2025-08-11 10:17:51.926444: 读卡器认证监听启动成功
2025-08-11 10:17:51.926444: 读者证、社保卡、电子社保卡 认证服务启动成功
2025-08-11 10:17:51.926444: 所有认证服务启动完成，成功启动 2 个服务
2025-08-11 10:17:51.926444: 当前可用的认证方式: 微信扫码、读者证、社保卡、电子社保卡
2025-08-11 10:17:51.926444: MultiAuthManager启动监听成功
2025-08-11 10:17:51.926444: 闸机认证服务启动成功（无UI模式），超时时间: 30秒
2025-08-11 10:17:51.926444: 闸机状态变更: GateState.enterScanning
2025-08-11 10:17:51.926444: 闸机认证服务启动成功
2025-08-11 10:17:51.927457: subThread :ReaderCommand.close
2025-08-11 10:17:51.927457: commandRsp:ReaderCommand.close
2025-08-11 10:17:51.927457: cacheUsedReaders:()
2025-08-11 10:17:51.927457: close:done
2025-08-11 10:17:51.927457: changeType:ReaderErrorType.closeSuccess
2025-08-11 10:17:51.927457: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-08-11 10:17:51.928441: already close all reader
2025-08-11 10:17:51.928441: subThread :ReaderCommand.readerList
2025-08-11 10:17:51.928441: commandRsp:ReaderCommand.readerList
2025-08-11 10:17:51.928441: readerList：3,readerSetting：3
2025-08-11 10:17:51.928441: cacheUsedReaders:3
2025-08-11 10:17:51.928441: subThread :ReaderCommand.open
2025-08-11 10:17:51.929447: commandRsp:ReaderCommand.open
2025-08-11 10:17:52.028565: dc_init:0xb4 100
2025-08-11 10:17:52.028565: open reader readerType ：10 ret：0
2025-08-11 10:17:52.028565: open reader readerType ：13 ret：0
2025-08-11 10:17:52.036367: 去开启 读 监听
2025-08-11 10:17:52.037363: widget.port.isOpen:true
2025-08-11 10:17:52.038731: 打开COM4读写成功
2025-08-11 10:17:52.038731: 发送：[16, 54, 0d]
2025-08-11 10:17:52.038731: open reader readerType ：12 ret：0
2025-08-11 10:17:52.038731: [[10, 0], [13, 0], [12, 0]]
2025-08-11 10:17:52.038731: changeType:ReaderErrorType.openSuccess
2025-08-11 10:17:52.039739: 读卡器状态变化: ReaderErrorType.openSuccess
2025-08-11 10:17:52.039739: 读卡器连接成功
2025-08-11 10:17:52.039739: 读卡器连接成功，确保扫描状态正常
2025-08-11 10:17:52.039739: 恢复读卡器扫描状态...
2025-08-11 10:17:52.039739: 读卡器扫描状态已恢复
2025-08-11 10:17:52.040727: subThread :ReaderCommand.untilDetected
2025-08-11 10:17:52.040727: commandRsp:ReaderCommand.untilDetected
2025-08-11 10:17:52.040727: subThread :ReaderCommand.resumeInventory
2025-08-11 10:17:52.040727: commandRsp:ReaderCommand.resumeInventory
2025-08-11 10:17:52.548587: dc_config_card:0
2025-08-11 10:17:52.564411: dc_card_n_hex:1,len:0
2025-08-11 10:17:52.564951: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:52.631925: iReadCardBas ret:4294967294
2025-08-11 10:17:52.631925: 无卡
2025-08-11 10:17:52.631925: 无卡
2025-08-11 10:17:53.044770: dc_config_card:0
2025-08-11 10:17:53.060794: dc_card_n_hex:1,len:0
2025-08-11 10:17:53.060794: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:53.128778: iReadCardBas ret:4294967294
2025-08-11 10:17:53.128778: 无卡
2025-08-11 10:17:53.128778: 无卡
2025-08-11 10:17:53.548226: dc_config_card:0
2025-08-11 10:17:53.564446: dc_card_n_hex:1,len:0
2025-08-11 10:17:53.564446: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:53.630770: iReadCardBas ret:4294967294
2025-08-11 10:17:53.631766: 无卡
2025-08-11 10:17:53.631766: 无卡
2025-08-11 10:17:54.044514: dc_config_card:0
2025-08-11 10:17:54.132022: dc_card_n_hex:0,len:4
2025-08-11 10:17:54.132996: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-11 10:17:54.140798: dc_authentication_pass:0
2025-08-11 10:17:54.147812: dc_read:0
2025-08-11 10:17:54.148799: data:31313131313100000000000000000000,coder:Std14443ACoder
2025-08-11 10:17:54.148799: parseRet：{"barCode":"111111"},decoder:14443AStd
2025-08-11 10:17:54.148799: 🎯 HWTagProvider.changeAddedItem: 收到 1 个新标签
2025-08-11 10:17:54.148799: 🏷️ 新标签[0]: uid=A577EB2D, barCode=111111, readerType=10
2025-08-11 10:17:54.149700: 🎯 HWTagProvider: 当前总标签数 1, 新增标签数 1
2025-08-11 10:17:54.149700: 检测到二维码数据: 111111
2025-08-11 10:17:54.150700: 未知的二维码类型: 111111
2025-08-11 10:17:54.150700: 检测到二维码数据: 111111
2025-08-11 10:17:54.150700: 未知的二维码类型: 111111
2025-08-11 10:17:54.150700: 检测到二维码数据: 111111
2025-08-11 10:17:54.150700: 未知的二维码类型: 111111
2025-08-11 10:17:54.150700: 检测到读卡器数据: 1条
2025-08-11 10:17:54.150700: 读卡器数据认证：
2025-08-11 10:17:54.150700:   设备类型: 10
2025-08-11 10:17:54.150700:   条码: 111111
2025-08-11 10:17:54.151697:   标签UID: A577EB2D
2025-08-11 10:17:54.151697:   对应登录类型: AuthLoginType.readerCard
2025-08-11 10:17:54.151697:   根据读卡器类型10确定认证方式为: 读者证
2025-08-11 10:17:54.151697:   开始调用认证API: 111111
2025-08-11 10:17:54.151697: 正在认证用户: 111111, 方式: 读者证
2025-08-11 10:17:54.151697: 多认证管理器: 切换显示方式 微信扫码 -> 读者证
2025-08-11 10:17:54.151697: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:54.151697: 认证状态变化: MultiAuthState.listening
2025-08-11 10:17:54.151697: 多认证管理器: 读者证获得认证请求锁
2025-08-11 10:17:54.152703: 63 CardType 值为空
2025-08-11 10:17:54.152703: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250811    101754  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY5AZF01D
2025-08-11 10:17:54.152703: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:54.214737: iReadCardBas ret:4294967294
2025-08-11 10:17:54.214737: 无卡
2025-08-11 10:17:54.214737: 无卡
2025-08-11 10:17:54.215739: 检测到了卡 来暂停 ：1
2025-08-11 10:17:54.671787: Rsp : 64              00120250811    101938000100010000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|AS10006811|JF0.0|BE|AF|AG|AY5AZDBB8
2025-08-11 10:17:54.688219: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250811    101938, HoldItemsCount: 0001, OverdueItemCount: 0001, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 5AZDBB8}
2025-08-11 10:17:54.688664: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250811    101938, HoldItemsCount: 0001, OverdueItemCount: 0001, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 5AZDBB8}
2025-08-11 10:17:54.688664: SIP2认证成功: 用户=gd, ID=111111
2025-08-11 10:17:54.689664:   认证结果: AuthStatus.success, 方式: 读者证
2025-08-11 10:17:54.689664: 认证成功，继续监听下一个用户
2025-08-11 10:17:54.689664: 恢复读卡器扫描状态...
2025-08-11 10:17:54.690661: 读卡器扫描状态已恢复
2025-08-11 10:17:54.690661: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-11 10:17:54.690661: 认证状态变化: MultiAuthState.authenticating
2025-08-11 10:17:54.690661: 认证状态变化: MultiAuthState.authenticating
2025-08-11 10:17:54.690661: 多认证管理器状态变更: authenticating
2025-08-11 10:17:54.690661: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-11 10:17:54.690661: 认证状态变化: MultiAuthState.completed
2025-08-11 10:17:54.690661: 认证状态变化: MultiAuthState.completed
2025-08-11 10:17:54.690661: 多认证管理器状态变更: completed
2025-08-11 10:17:54.690661: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-11 10:17:54.691658: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-11 10:17:54.691658: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-11 10:17:54.691658: 认证状态变化: MultiAuthState.authenticating
2025-08-11 10:17:54.691658: 认证状态变化: MultiAuthState.authenticating
2025-08-11 10:17:54.691658: 多认证管理器状态变更: authenticating
2025-08-11 10:17:54.691658: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-11 10:17:54.691658: 认证状态变化: MultiAuthState.completed
2025-08-11 10:17:54.691658: 认证状态变化: MultiAuthState.completed
2025-08-11 10:17:54.691658: 多认证管理器状态变更: completed
2025-08-11 10:17:54.691658: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-11 10:17:54.691658: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-11 10:17:54.691658: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-11 10:17:54.691658: 认证状态变化: MultiAuthState.authenticating
2025-08-11 10:17:54.692677: 认证状态变化: MultiAuthState.authenticating
2025-08-11 10:17:54.692677: 多认证管理器状态变更: authenticating
2025-08-11 10:17:54.692677: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-11 10:17:54.692677: 认证状态变化: MultiAuthState.completed
2025-08-11 10:17:54.692677: 认证状态变化: MultiAuthState.completed
2025-08-11 10:17:54.692677: 多认证管理器状态变更: completed
2025-08-11 10:17:54.692677: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-11 10:17:54.692677: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-11 10:17:54.692677: 非认证状态收到认证结果，忽略
2025-08-11 10:17:54.692677: 收到认证结果: AuthStatus.success, 用户: gd
2025-08-11 10:17:54.692677: 收到闸机认证结果: AuthStatus.success, 用户: gd
2025-08-11 10:17:54.692677: 闸机状态变更: GateState.enterOpening
2025-08-11 10:17:54.692677: 页面状态变更: SilencePageState.authSuccess
2025-08-11 10:17:54.693682: 发送闸机命令: GateCommand.enterOpen, 数据: aa 00 02 01 00 00 48 72
2025-08-11 10:17:54.693682: 发送数据失败: 期望发送8字节，实际发送7字节
2025-08-11 10:17:54.693682: 非认证状态收到认证结果，忽略
2025-08-11 10:17:54.694176: 串口错误: SEND_INCOMPLETE - 发送数据不完整: 期望8字节，实际7字节
2025-08-11 10:17:54.694176: 闸机命令发送失败: enter_open (硬件可能未连接，但不影响系统功能)
2025-08-11 10:17:54.694176: 收到认证结果: AuthStatus.success, 用户: gd
2025-08-11 10:17:54.694176: 非认证状态收到认证结果，忽略
2025-08-11 10:17:54.694176: 非认证状态收到认证结果，忽略
2025-08-11 10:17:54.694176: 收到认证结果: AuthStatus.success, 用户: gd
2025-08-11 10:17:54.695176: 非认证状态收到认证结果，忽略
2025-08-11 10:17:54.695176: subThread :ReaderCommand.resumeInventory
2025-08-11 10:17:54.695176: commandRsp:ReaderCommand.resumeInventory
2025-08-11 10:17:55.044408: dc_config_card:0
2025-08-11 10:17:55.140610: dc_card_n_hex:0,len:4
2025-08-11 10:17:55.140610: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-08-11 10:17:55.148404: dc_authentication_pass:0
2025-08-11 10:17:55.156720: dc_read:0
2025-08-11 10:17:55.157032: iReadCardBas: start ,isOpenComPort:false
2025-08-11 10:17:55.224165: iReadCardBas ret:4294967294
2025-08-11 10:17:55.224165: 无卡
2025-08-11 10:17:55.224165: 无卡
2025-08-11 10:17:55.225166: 检测到了卡 来暂停 ：1
2025-08-11 10:17:55.225166: 检测到二维码数据: 111111
2025-08-11 10:17:55.225166: 未知的二维码类型: 111111
2025-08-11 10:17:55.225166: 检测到二维码数据: 111111
2025-08-11 10:17:55.225166: 未知的二维码类型: 111111
2025-08-11 10:17:55.225166: 检测到二维码数据: 111111
2025-08-11 10:17:55.225166: 未知的二维码类型: 111111
2025-08-11 10:17:57.692301: 停止闸机认证服务...
2025-08-11 10:17:57.692301: 闸机状态变更: GateState.idle
2025-08-11 10:17:57.692301: 页面状态变更: SilencePageState.welcome
2025-08-11 10:17:57.692301: 认证监听未在运行中
2025-08-11 10:17:57.692301: 闸机认证服务已停止
2025-08-11 10:17:59.690910: 多认证管理器: 认证请求锁已释放（之前为读者证）
2025-08-11 10:17:59.690910: 多认证管理器: 还原到默认显示方式: 微信扫码
2025-08-11 10:18:02.692059: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-08-11 10:18:02.692882: 多认证管理器状态变更: listening
2025-08-11 10:18:02.692882: ⚠️ 人脸识别服务未初始化或不可用
2025-08-11 10:18:10.573177: 收到串口命令: enter_start (进馆开始)
2025-08-11 10:18:10.574178: 处理进馆开始
2025-08-11 10:18:10.574178: 闸机状态变更: GateState.enterStarted
2025-08-11 10:18:10.574178: 页面状态变更: SilencePageState.authenticating
2025-08-11 10:18:10.574178: MultiAuthManager当前状态: MultiAuthState.listening
2025-08-11 10:18:10.574178: 启动闸机认证服务...
2025-08-11 10:18:10.574178: MultiAuthManager当前状态: MultiAuthState.listening
2025-08-11 10:18:10.574178: 开始启动MultiAuthManager监听...
2025-08-11 10:18:10.574178: 认证监听已在运行中
2025-08-11 10:18:10.575174: MultiAuthManager启动监听成功
2025-08-11 10:18:10.575174: 闸机认证服务启动成功（无UI模式），超时时间: 30秒
2025-08-11 10:18:10.575174: 闸机状态变更: GateState.enterScanning
2025-08-11 10:18:10.575174: 闸机认证服务启动成功
2025-08-11 10:18:19.525107: 发送心跳
2025-08-11 10:18:19.525107: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY6AZFC9B
2025-08-11 10:18:19.550193: Rsp : 98YYYNNN00500320250811    1020032.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY6AZD52D
